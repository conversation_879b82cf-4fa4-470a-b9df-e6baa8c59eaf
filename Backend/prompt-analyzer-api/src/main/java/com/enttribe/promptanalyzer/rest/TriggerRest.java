package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.trigger.TriggerRequestDto;
import com.enttribe.promptanalyzer.dto.trigger.TriggerResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing trigger operations.
 * Provides endpoints for creating, updating, searching, and counting triggers.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "TriggerRest", url = "${prompt-analyzer-service.url}", path = "/trigger", primary = false)
public interface TriggerRest {

    /**
     * Searches for triggers with pagination and sorting options.
     * Requires ROLE_API_TRIGGER_READ security role.
     *
     * @param filter    Optional filter criteria for searching triggers
     * @param offset    Required pagination offset
     * @param size      Required pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching triggers as TriggerResponseDto objects
     * @apiNote Response Codes:
     * 200 - Search Trigger successfully
     * 500 - Error occurred during search
     */
    @GetMapping(path = "/search")
    @Operation(summary = "Search Trigger", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TRIGGER_SEARCH})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Search Trigger successfully based on the provided parameters like filtering, and sorting"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    List<TriggerResponseDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Counts the number of triggers matching the optional filter.
     * Requires ROLE_API_TRIGGER_READ security role.
     *
     * @param filter Optional filter criteria
     * @return Total count of matching triggers
     * @apiNote Response Codes:
     * 200 - Count All Trigger successfully
     * 500 - Error occurred during count operation
     */
    @GetMapping(path = "/count")
    @Operation(summary = "Count All Triggers", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TRIGGER_COUNT})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count All Trigger successfully."),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    Long count(
            @RequestParam(required = false) String filter);

    /**
     * Creates a new trigger.
     * Requires ROLE_API_TRIGGER_WRITE security role.
     *
     * @param dto The trigger request data to create
     * @return Map containing the result of the create operation
     * @apiNote Response Codes:
     * 200 - Create Trigger successfully
     * 500 - Error occurred during creation
     */
    @Operation(summary = "Create new Trigger", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TRIGGER_CREATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Create Trigger successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> save(@RequestBody TriggerRequestDto dto);

    /**
     * Updates an existing trigger.
     * Requires ROLE_API_TRIGGER_WRITE security role.
     *
     * @param dto The updated trigger request data
     * @return Map containing the result of the update operation
     * @apiNote Response Codes:
     * 200 - Update Trigger successfully
     * 500 - Error occurred during update
     */
    @Operation(summary = "Update Trigger", security = {
            @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_TRIGGER_UPDATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Update Trigger successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/update", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, String> update(@RequestBody TriggerRequestDto dto);

    /**
     * Retrieves a trigger by its name.
     * Requires ROLE_API_TRIGGER_READ security role.
     *
     * @param name The name of the trigger to retrieve
     * @return TriggerResponseDto containing the trigger details
     */
    @Operation(
            summary = "Retrieve trigger by name",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TRIGGER_GET_BY_NAME})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Trigger retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/getTriggerByName/{name}")
    TriggerResponseDto getTriggerByName(@PathVariable String name);

}
