package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.processor.ProcessorRequestDto;
import com.enttribe.promptanalyzer.dto.processor.ProcessorResponseDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * REST client interface for processor operations.
 * Provides CRUD operations for managing processors through Feign client.
 * Author: VisionWaves
 * Version: 1.0
 */
@FeignClient(name = "ProcessorRest", url = "${prompt-analyzer-service.url}", path = "/processor", primary = false)
public interface ProcessorRest {

    /**
     * Saves a new processor.
     *
     * @param requestDto the processor request data transfer object
     * @return a map containing the result of the save operation
     */
    @Operation(
            summary = "Save a new processor",
            description = "Saves a new processor to the system.",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_PROCESSOR_CREATE}
                )
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Processor saved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/save")
    Map<String, Object> save(@RequestBody ProcessorRequestDto requestDto);

    /**
     * Updates an existing processor.
     *
     * @param id         the ID of the processor to update
     * @param requestDto the processor request data transfer object
     * @return a map containing the result of the update operation
     */
    @Operation(
            summary = "Update an existing processor",
            description = "Updates an existing processor in the system.",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_PROCESSOR_UPDATE}
                )
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Processor updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/update/{id}")
    Map<String, Object> update(@PathVariable Integer id, @RequestBody ProcessorRequestDto requestDto);

    /**
     * Searches for processors based on the provided filter and pagination parameters.
     *
     * @param filter    optional filter for searching processors
     * @param offset    the starting point for pagination
     * @param size      the number of processors to retrieve
     * @param orderBy   optional field to order the results by
     * @param orderType optional type of ordering (asc/desc)
     * @return a list of processor response data transfer objects
     */
    @Operation(
            summary = "Search for processors",
            description = "Retrieves processors based on the provided criteria.",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_PROCESSOR_SEARCH}
                )
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Processors retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/search")
    List<ProcessorResponseDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Counts the number of processors based on the provided filter.
     *
     * @param filter optional filter for counting processors
     * @return the count of processors
     */
    @Operation(
            summary = "Count processors",
            description = "Counts the number of processors based on the provided criteria.",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_PROCESSOR_COUNT}
                )
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/count")
    Long count(@RequestParam(required = false) String filter);

}
