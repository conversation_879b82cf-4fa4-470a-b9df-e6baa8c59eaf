package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


import java.util.Map;

/**
 * REST controller for parsing time-related text.
 * Provides an endpoint for parsing user queries for time dimensions.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "TimeParserRest", url = "${prompt-analyzer-service.url}", path = "/time-parser", primary = false)
public interface TimeParserRest {

    /**
     * Parses a user query for time dimensions.
     *
     * @param request Map containing the user query
     * @return Map containing the parsed time information
     */
    @Operation(
            summary = "Parse time from user query",
            description = "Parses a user query for time dimensions and returns structured time information.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TIME_PARSER_PARSE})
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = APIConstants.SUCCESS),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/parse")
    Map<String, String> parseTime(@RequestBody Map<String, String> request);

    /**
     * Adds year to a query if it's missing, using a hardcoded timezone.
     *
     * @param request The user query to process
     * @return The processed query with year added if missing
     */
    @Operation(
            summary = "Add year to query if missing",
            description = "Processes a query and adds the year if it's missing, using a hardcoded timezone.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_TIME_PARSER_UPDATE})
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = APIConstants.SUCCESS),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping("/add-year")
    Map<String, String> addYearIfMissing(@RequestBody Map<String, String> request);

}
