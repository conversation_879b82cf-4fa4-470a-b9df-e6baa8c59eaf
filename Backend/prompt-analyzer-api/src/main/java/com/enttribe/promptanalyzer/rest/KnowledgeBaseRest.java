package com.enttribe.promptanalyzer.rest;

import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing knowledge base operations.
 * Provides endpoints for creating, updating, searching, and managing knowledge base documents.
 *
 * <AUTHOR>
 * @version 1.0
 */
@FeignClient(name = "KnowledgeBaseRest", url = "${prompt-analyzer-service.url}", path = "/knowledge-base", primary = false)
public interface KnowledgeBaseRest {

    /**
     * Retrieves knowledge base data by ID.
     *
     * @param id The ID of the knowledge base entry
     * @return KnowledgeBaseResponseDto containing the requested data
     */
    @Operation(
            summary = "Get knowledge base entry by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_GET_BY_ID})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Knowledge base entry retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping(path = "/findById/{id}")
    KnowledgeBaseResponseDto getKnowledgBaseById(@PathVariable Integer id);

    @Operation(
            summary = "Process website knowledge base",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_CREATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Website knowledge base processed successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @GetMapping(path = "/processWebSiteKB")
    Map<String, String> processWebSiteKB();

    /**
     * Saves a new document to the knowledge base.
     *
     * @param documentDto The document data and file to save
     * @return Map containing the result of the save operation
     */
    @Operation(
            summary = "Save document to knowledge base",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_CREATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Document saved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/saveDocument")
    Map<String, String> saveDocument(@ModelAttribute DocumentRequestDto documentDto);

    @Operation(
            summary = "Save website to knowledge base",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_CREATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Website saved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path="/saveWebSite")
    Map<String, String> saveWebSite(@RequestBody DocumentRequestDto documentDto);


    /**
     * Updates an existing document in the knowledge base.
     *
     * @param id          The ID of the document to update
     * @param documentDto The updated document data and file
     * @return Map containing the result of the update operation
     * @throws JsonProcessingException if there's an error processing JSON data
     */
    @Operation(
            summary = "Update document in knowledge base",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_UPDATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Document updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/updateDocument/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Map<String, String> updateDocument(@PathVariable Integer id, @ModelAttribute DocumentRequestDto documentDto) throws JsonProcessingException;

    /**
     * Counts knowledge base entries matching the optional filter.
     *
     * @param filter Optional filter criteria
     * @return Total count of matching entries
     */
    @Operation(
            summary = "Count knowledge base entries",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_COUNT})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Count retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/count")
    Long count(@RequestParam(required = false) String filter);

    /**
     * Searches knowledge base entries with pagination and sorting.
     *
     * @param filter    Optional filter criteria
     * @param offset    Required pagination offset
     * @param size      Required pagination size
     * @param orderBy   Optional field to order results by
     * @param orderType Optional order direction (asc/desc)
     * @return List of matching knowledge base entries
     */
    @Operation(
            summary = "Search knowledge base entries",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_SEARCH})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Knowledge base entries retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/search")
    List<KnowledgeBaseResponseDto> search(
            @RequestParam(required = false) String filter,
            @RequestParam(required = true) Integer offset,
            @RequestParam(required = true) Integer size,
            @RequestParam(required = false) String orderBy,
            @RequestParam(required = false) String orderType);

    /**
     * Soft deletes a knowledge base entry by ID.
     *
     * @param id The ID of the entry to delete
     * @return Map containing the result of the delete operation
     */
    @Operation(
            summary = "Delete knowledge base entry by ID",
            security = {
                @SecurityRequirement(
                    name = APIConstants.DEFAULT_SCHEME,
                    scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_DELETE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Knowledge base entry deleted successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/deleteById/{id}")
    Map<String, String> deleteById(@PathVariable Integer id);

    /**
     * Retrieves multiple knowledge base entries by their IDs.
     *
     * @param ids List of knowledge base IDs to retrieve
     * @return List of matching knowledge base entries in SDK format
     */
    @Operation(
            summary = "Get knowledge base entries by IDs",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_GET_BY_IDS})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Knowledge base entries retrieved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/getKnowledgeBaseByIds")
    List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(@RequestBody List<Integer> ids);

    @Operation(
            summary = "Update tag by ID",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_UPDATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Tag updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping(path = "/updateTagById/{id}")
    Map<String, String> updateTagById(@PathVariable Integer id, @RequestBody Map<String, String> tags);


    /**
     * Retrieves the names of tables based on the provided request parameters.
     *
     * @param requestBody A map containing request parameters
     * @return List of table names
     */
    @Operation(
            summary = "Get Table Names",
            description = "Retrieves the names of tables based on the provided request parameters.",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_SHOW_TABLES})
            }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved table names"),
            @ApiResponse(responseCode = "500", description = "Error occurred during retrieval")
    })
    @PostMapping("/show-tables")
    List<String> getTablesName(Map<String, String> requestBody);

    @Operation(
            summary = "Check if a knowledge base URL exists",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_EXISTS})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Existence check completed successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/existsWebsiteUrl", consumes = MediaType.APPLICATION_JSON_VALUE)
    Map<String, Object> existsWebsiteUrl(@RequestBody Map<String, String> requestMap);

    /**
     * Saves a new content to the knowledge base.
     *
     * @param documentDto The document data and content to save
     * @return Map containing the result of the save operation
     */
     @Operation(
            summary = "Save content to knowledge base",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_CREATE})})
     @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Content saved successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
     @PostMapping(path = "/saveContent", consumes = MediaType.APPLICATION_JSON_VALUE)
     Map<String, String> saveContent(@RequestBody DocumentRequestDto documentDto);


    @Operation(
            summary = "Import CSV data to Milvus vector database",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_MILVUS_IMPORT_DATA})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "CSV data imported to Milvus successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)})
    @PostMapping("/milvusImportCsv")
    ResponseEntity<String> milvusImportCsv(@RequestParam("file") MultipartFile file);

    /**
     * Searches knowledge base by name and answers user question using vector metadata.
     *
     * @param requestMap Map containing knowledgeBaseName and userQuestion
     * @return Response containing the answer based on vector search
     */
    @Operation(
            summary = "Get direct answer from knowledge base using vector search",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_SEARCH})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Answer generated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/getAnswer")
    Map<String, String> getAnswer(@RequestBody Map<String, String> requestMap);

    /**
     * Exports knowledge bases as CSV.
     *
     * @return CSV file as a downloadable resource
     */
    @Operation(
            summary = "Export knowledge bases as CSV",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_EXPORT})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Knowledge bases exported successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    ResponseEntity<Resource> exportKnowledgeBases(@RequestBody List<Integer> ids) ;

    /**
     * Imports knowledge bases from CSV file.
     *
     * @param file CSV file containing knowledge base data
     * @return Map containing import results
     */
    @Operation(
            summary = "Import knowledge bases from CSV",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_IMPORT})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Knowledge bases imported successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<Resource> importKnowledgeBases(@RequestParam("file") MultipartFile file);

    /**
     * Updates the return direct flag for a knowledge base entry.
     *
     * @param requestMap Map containing the ID and value of the return direct flag
     * @return Map containing the result of the update operation
     */
    @Operation(
            summary = "Update return direct flag for knowledge base entry",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_UPDATE})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Return direct flag updated successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @PostMapping(path = "/updateReturnDirect")
    Map<String, String> updateReturnDirect(@RequestBody Map<String,String> requestMap);

    /**
     * Checks if a knowledge base name exists or not.
     *
     * @param name The name of the knowledge base to check
     * @return Map containing the result of the existence check
     */
    @Operation(
            summary = "Check if a knowledge base name exists or not",
            security = {
                    @SecurityRequirement(
                            name = APIConstants.DEFAULT_SCHEME,
                            scopes = {APIConstants.ROLE_API_KNOWLEDGE_BASE_EXISTS_BY_NAME})})
    @ApiResponses(value = {
            @ApiResponse(responseCode = APIConstants.SUCCESS_CODE, description = "Existence check completed successfully"),
            @ApiResponse(responseCode = APIConstants.ERROR_CODE, description = APIConstants.ERROR)
    })
    @GetMapping(path = "/existsKnowledgeBaseName/{name}")
    Map<String, Boolean> existsKnowledgeBaseName(@PathVariable String name);

}