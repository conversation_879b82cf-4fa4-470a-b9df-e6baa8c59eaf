package com.enttribe.promptanalyzer.service;

import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.TestCase;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * Manages test case operations and validation scenarios.
 * This service handles the creation, updating, and management of test cases,
 * providing functionality for test case tracking and execution with search capabilities.
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface TestCaseService {

    Map<String, String> create(TestCaseRequestDto requestDto);

    Map<String, String> update(TestCase updatedTestcase);

    Map<String, String> deleteTestcase(Integer id);

    List<TestCase> search(String filter, Integer offset, Integer size, String orderBy, String orderType);

    Long count(String filter);

    Map<String, String> createBatch(List<TestCaseRequestDto> requestDtos);

    /**
     * Imports test cases from a CSV file.
     *
     * @param file The CSV file containing test case data
     * @return ResponseEntity containing the result of the import operation
     */
    ResponseEntity<Resource> importTestCase(MultipartFile file);
    ResponseEntity<Resource> exportTestCasesByIds(List<Integer> testCaseIds);

    /**
     * Exports test cases based on the provided filter criteria.
     *
     * @param filter The filter criteria to apply
     * @param orderBy The field to order the results by
     * @param orderType The type of ordering (ascending/descending)
     * @return ResponseEntity containing the exported test cases as a CSV file
     */
}
