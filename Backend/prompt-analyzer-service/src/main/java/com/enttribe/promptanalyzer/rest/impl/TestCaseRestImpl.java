package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.rest.TestCaseRest;
import com.enttribe.promptanalyzer.service.TestCaseService;
import com.enttribe.promptanalyzer.util.TestCaseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * REST controller for managing test case operations.
 * Provides endpoints for creating, updating, deleting, and searching test cases.
 * All endpoints require appropriate security roles for access.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestController
@RequestMapping(produces = MediaType.APPLICATION_JSON_VALUE, path = "/test-case")
@RequiredArgsConstructor
public class TestCaseRestImpl implements TestCaseRest {

    private final TestCaseService testCaseService;

    /**
     * Creates a new test case.
     *
     * @param requestDto the test case request data transfer object
     * @return a map containing the result of the creation
     */
    @Override
    public Map<String, String> create(TestCaseRequestDto requestDto) {
        log.info("Creating test case");
        return testCaseService.create(requestDto);
    }

    /**
     * Creates a test case in batch.
     *
     * @param requestDtos the test case request data transfer object
     * @return a map containing the result of the creation
     */
    @Override
    public Map<String, String> createBatch(List<TestCaseRequestDto> requestDtos) {
        log.debug("Inside @method createBatch");
        return testCaseService.createBatch(requestDtos);
    }

    /**
     * Updates an existing test case.
     *
     * @param updatedTestcase the test case to be updated
     * @return a map containing the result of the update
     */
    @Override
    public Map<String, String> update(TestCase updatedTestcase) {
        log.info("Updating test case");
        return testCaseService.update(updatedTestcase);
    }

    /**
     * Deletes a test case by its ID.
     *
     * @param requestBody a map containing the ID of the test case to be deleted
     * @return a map containing the result of the deletion
     */
    @Override
    public Map<String, String> deleteTestcase(Map<String, Integer> requestBody) {
        Integer id = requestBody.get("id");
        log.info("Deleting test case with ID: {}", id);
        return testCaseService.deleteTestcase(id);
    }

    /**
     * Searches for test cases based on the provided filter and pagination options.
     *
     * @param filter the search filter
     * @param offset the offset for pagination
     * @param size the number of results to return
     * @param orderBy the field to order the results by
     * @param orderType the type of ordering (ascending or descending)
     * @return a list of test case request data transfer objects matching the search criteria
     */
    @Override
    public List<TestCaseRequestDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        log.info("Searching test cases");
        List<TestCase> testCases = testCaseService.search(filter, offset, size, orderBy, orderType);
        return TestCaseUtils.getTestCaseDtoList(testCases);
    }

    /**
     * Counts the number of test cases matching the provided filter.
     *
     * @param filter the search filter
     * @return the count of test cases matching the filter
     */
    @Override
    public Long count(String filter) {
        return testCaseService.count(filter);
    }

    /**
     * Exports test cases by their IDs.
     *
     * @param testCaseIds List of test case IDs to export
     * @return ResponseEntity containing the exported test cases as a CSV file
     */
    @Override
    public ResponseEntity<Resource> exportTestCasesByIds(List<Integer> testCaseIds) {
        log.info("Exporting test cases by IDs: {}", testCaseIds);
        return testCaseService.exportTestCasesByIds(testCaseIds);
    }

    /**
     * Imports test cases from a CSV file.
     *
     * @param file The CSV file containing test case data
     * @return ResponseEntity containing the result of the import operation
     */
    @Override
    public ResponseEntity<Resource> importTestCases(MultipartFile file) {
        log.info("Importing test cases from file: {}", file.getOriginalFilename());
        return testCaseService.importTestCase(file);
    }

}
