package com.enttribe.promptanalyzer.config;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.reflections.Reflections;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import com.enttribe.product.pii.filter.PropertyFilter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.Scopes;
import io.swagger.v3.oas.models.security.SecurityScheme;

@Configuration
public class SwaggerConfig implements WebMvcConfigurer{

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(SwaggerConfig.class);

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/lib/**")
                .addResourceLocations("classpath:/BOOT-INF/lib/");
    }

    private List<String> packagesToScan = List.of("com.enttribe.promptanalyzer");


    @Bean
    public ObjectMapper objectMapper() {
        logger.info("Custom Object Mapper initialised ");
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new Jdk8Module());
        SimpleFilterProvider filterProvider = new SimpleFilterProvider();
        filterProvider.setFailOnUnknownId(false);
        FilterProvider filters = filterProvider.addFilter("propertyFilter", new PropertyFilter());
        objectMapper.setFilterProvider(filters);
        return objectMapper;
    }

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI().info(new Info().title("prompt-analyzer").version("1.0.0"))
                .components(new Components().addSecuritySchemes("default", createOAuthSecurityScheme()));
    }

    SecurityScheme createOAuthSecurityScheme() {
        Scopes scopesArray = new Scopes();
        OAuthFlow oAuthFlow = new OAuthFlow().authorizationUrl("http://localhost/auth");
        try {
            logger.debug("Permission SCopes Size is {}", scopesArray.size());
            oAuthFlow.scopes(scopesArray);
            Scopes scopes = readAuthorizationScopes(packagesToScan, new Scopes());
            logger.info("Authorization Scopes  is {}", scopes.values());
            oAuthFlow.scopes(scopes);
        } catch (Exception e) {
            logger.error("error while getting permission {}", e.getMessage(), e);
        }
        return new SecurityScheme()
                .type(SecurityScheme.Type.OAUTH2)
                .description("Oauth2 flow")
                .flows(new OAuthFlows().implicit(oAuthFlow));
    }

    private static Scopes readAuthorizationScopes(List<String> packageNames, Scopes scopesArray) {
        List<String> scopes = new ArrayList<>();
        for (String packageName : packageNames) {
            Reflections reflections = new Reflections(packageName);
            Set<Class<?>> classes = reflections.getTypesAnnotatedWith(FeignClient.class);
            for (Class<?> clazz : classes) {
                processAuth(scopesArray, scopes, clazz);
            }
        }
        return scopesArray;
    }

    private static void processAuth(Scopes scopesArray, List<String> scopes, Class<?> clazz) {
        for (java.lang.reflect.Method method : clazz.getDeclaredMethods()) {
            if (method.isAnnotationPresent(Operation.class)) {
                Operation apiOperation = method.getAnnotation(Operation.class);
                if (apiOperation.security().length > 0) {
                    io.swagger.v3.oas.annotations.security.SecurityRequirement[] authorizations = apiOperation.security();
                    processAuthScop(scopesArray, scopes, authorizations);
                }
            }
        }
    }

    private static void processAuthScop(Scopes scopesArray, List<String> scopes,
                                        io.swagger.v3.oas.annotations.security.SecurityRequirement[] authorizations) {
        for (io.swagger.v3.oas.annotations.security.SecurityRequirement authorization : authorizations) {
            if (authorization.scopes().length > 0) {
                String[] authorizationScopes = authorization.scopes();
                for (String scope : authorizationScopes) {
                    scopesArray.addString(scope, scope);
                    scopes.add(scope);
                }
            }
        }
    }
}



