package com.enttribe.promptanalyzer.service.impl;

import com.opencsv.CSVReader;
import com.opencsv.CSVWriter;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
public class TimeParserTestRunner {

    private final TimeParserServiceImpl timeParser;

    public TimeParserTestRunner(TimeParserServiceImpl timeParser) {
        this.timeParser = timeParser;
    }

    public void runTests(String inputCsv, String outputCsv) {
        try (CSVReader reader = new CSVReader(new FileReader(inputCsv));
             CSVWriter writer = new CSVWriter(new FileWriter(outputCsv))) {

            writer.writeNext(new String[]{"Input Query", "Expected From", "Expected To", "Actual From", "Actual To", "Result"});

            String[] row;
            while ((row = reader.readNext()) != null) {
                String query = row[0];
                String expectedFrom = row.length > 1 ? row[1] : "";
                String expectedTo = row.length > PromptConstants.EXPECTED_TO_COLUMN_INDEX ? row[PromptConstants.EXPECTED_TO_COLUMN_INDEX] : "";

                Map<String, String> req = new HashMap<>();
                req.put("userQuery", query);
                req.put("timeZone", "Asia/Riyadh");
                req.put("locale", "en_US");

                String result = timeParser.parseTimeFromQuery(req);
                String actualFrom = extractDate(result, "from:");
                String actualTo = extractDate(result, "to:");

                boolean passed = expectedFrom.equals(actualFrom) &&
                        (expectedTo.isEmpty() || expectedTo.equals(actualTo));

                writer.writeNext(new String[]{
                        query, expectedFrom, expectedTo, actualFrom, actualTo, passed ? "PASS" : "FAIL"
                });
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String extractDate(String result, String label) {
        if (result == null || !result.contains(label)) return "";

        int idx = result.indexOf(label) + label.length();
        int end;

        if ("from:".equals(label)) {
            end = result.indexOf("to:", idx);
        } else {
            end = result.length();
        }

        if (end == -1 || idx >= end) return "";

        return result.substring(idx, end).trim();
    }


    public static void main(String[] args) {
        TimeParserServiceImpl timeParser = new TimeParserServiceImpl(); // Inject dependencies as needed
        TimeParserTestRunner runner = new TimeParserTestRunner(timeParser);

        runner.runTests("/Users/<USER>/Documents/Work/prompt-analyzer/Backend/test_temporal/test_data.csv",
                "/Users/<USER>/Documents/Work/prompt-analyzer/Backend/test_temporal/test_result.csv");
        log.info("Test results written to test_results.csv");
    }
}