package com.enttribe.promptanalyzer.service.impl;


import com.enttribe.promptanalyzer.advisors.QuestionAnswerAdvisor;
import com.enttribe.promptanalyzer.constants.APIConstants;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.KnowledgeBaseDao;
import com.enttribe.promptanalyzer.dto.knowledge_base.DocumentRequestDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.VectorResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseResponseDto;
import com.enttribe.promptanalyzer.dto.knowledge_base.KnowledgeBaseSdkDto;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.ChunkCounter;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.KnowledgeConverter;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.KnowledgeBase;
import com.enttribe.promptanalyzer.service.KnowledgeBaseService;
import com.enttribe.promptanalyzer.util.SdkUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.opencsv.CSVReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xwpf.usermodel.IBodyElement;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.document.Document;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Implementation of the {@link KnowledgeBaseService} interface.
 * This class provides the actual business logic for managing KnowledgeBase.
 * for a specific application. It interacts with the data access layer to fetch and modify KnowledgeBase data.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    public static final String CUSTOM_AGENT_FILTER = "f0b90d7dc3864b18a1c59cf99b497324abc";
    public static final String CUSTOM_AGENT_FILTER_KEY = "custom_agent_id";
    private final KnowledgeBaseDao knowledgeDao;
    private final CustomFilter customFilter;
    private final VectorStore vectorStore;
    private final ApiService apiService;
    private final InferenceManager inferenceManager;
    private final S3Service s3Service;
    private final ChunkCounter chunkCounter;

    private static final String KNOWLEDGE_BASE_FILTER_KEY = "doc_id";

    private static final int DEFAULT_CHUNK_SIZE = 2000;
    private static final int MAX_TOKENS_PER_CHUNK = 510;
    private static final int MID_DIVISOR = 2;
    private static final int LARGE_CHUNK_SIZE = 4000;
    private static final int BATCH_SIZE = 10;
    private static final int MAX_IMPORT_RECORDS = 100;

    @Value("${spring.ai.vectorstore.milvus.databaseName}")
    private String databaseName;

    @Value("${spring.ai.vectorstore.milvus.collectionName}")
    private String collectionName;

    private static final List<String> csvColumnHeader = List.of(
            "Name", "Description", "VectorMetaData", "TopK", "SimilarityThreshold", "Type",
            "webSiteUrl", "fileName", "websiteTaskStatus", "websiteTaskId", "websiteTaskError",
            PromptConstants.COLLECTION_NAME, "docType", "docId", "filter", "docMetaData", "isContext",
            "tables", "integration", "returnDirect");

    /**
     * Retrieves a KnowledgeBase entry by its ID.
     *
     * @param id the ID of the knowledge base to retrieve
     * @return a DTO containing the knowledge base details
     * @throws BusinessException if the knowledge base with the given ID is not found
     */
    @Override
    public KnowledgeBaseResponseDto getKnowledgBaseById(Integer id) {
        KnowledgeBase knowledgeBase = knowledgeDao.findById(id).orElseThrow(() -> new BusinessException("Knowledge is not found for id : " + id));
        return KnowledgeConverter.convertKnowledgeDto(knowledgeBase);
    }

    /**
     * Saves a document into the knowledge base after extracting content from the file.
     * Supports the following file types: PDF, TXT, and DOCX.
     * The content is saved in a vector store and the document metadata is saved in the database.
     *
     * @param dto the DTO containing the document details and file
     * @return a map with the result of the operation ("success" or "failed")
     * @throws BusinessException if an error occurs while processing the file
     */
    @Override
    public Map<String, String> saveDocument(DocumentRequestDto dto) {
        log.debug("Inside @class KnowledgeBaseServiceImpl @method saveDocument");
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        Map<String, String> result = new HashMap<>();
        List<List<Document>> documents = new ArrayList<>();
        List<String> s3StorageFileNames = new ArrayList<>();
        try {
            mapToEntity(dto, knowledgeBase);
            switch (dto.getType()) {
                case "DOCUMENT" -> {
                    List<MultipartFile> files = dto.getFile();
                    Set<String> fileNames = extractFileNames(files);

                    for (MultipartFile file : files) {
                        fileNames.add(file.getOriginalFilename());
                        List<Document> content = extractContentFromFile(file);
                        if (content != null && !content.isEmpty()) {
                            documents.add(content);
                        }
                    }

                    knowledgeBase.setFileName(String.join(", ", fileNames)); // Join file names with a comma
                    log.debug("All files: {}", fileNames);

                    VectorResponseDto vectorResponse = saveInVectorFromDocReader(documents);
                    log.info("Going to save file on s3service");
                    s3StorageFileNames = s3Service.uploadFileToS3(files,vectorResponse.getFilter(),"");
                    knowledgeBase.setS3FileNames(JsonUtils.convertToJSON(s3StorageFileNames));
                    knowledgeBase.setFilter(vectorResponse.getFilter());
                    knowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
                    knowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));
                }
                case "SQL" -> {
                    List<String> tableNames = dto.getTables();
                    List<String> docIds = new ArrayList<>();
                    for (String tableName : tableNames) {
                        Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, tableName);
                        String tableSchema = apiService.getTableSchema(dto.getIntegration(), tableName);
                        Document document = new Document(tableSchema);
                        document.getMetadata().putAll(docMetaData);
                        docIds.add(document.getId());
                        vectorStore.accept(List.of(document));
                        log.debug("Document is successfully save in vector. table : {}", tableName);
                    }
                    knowledgeBase.setDocId(JsonUtils.convertToJSON(docIds));
                    knowledgeBase.setDescription(String.format("""
                            Tool is designed to assist an LLM in constructing valid SQL queries by providing the schema details of a specified database table.
                            Given a table name as input, this tool retrieves and returns its schema, including column names, data types, constraints, and other relevant metadata.
                            It contains schema for following tables:
                            %s
                            """, tableNames));
                    knowledgeBase.setTables(JsonUtils.convertToJSON(tableNames));
                    knowledgeBase.setIntegration(dto.getIntegration());
                }
                case "WEBSITE" -> processWebsiteData(dto, knowledgeBase);
                case "COLLECTION" -> {
                    Map<String, String> vectorMetaData = Map.of(
                           PromptConstants.VECTOR_DATABASE, PromptConstants.MILVUS,
                            PromptConstants.DATABASE_NAME, databaseName,
                            PromptConstants.COLLECTION_NAME, dto.getCollectionName(),
                            PromptConstants.EMBEDDING_MODEL_KEY, PromptConstants.EMBEDDING_MODEL,
                            PromptConstants.CHAT_MODEL_KEY, PromptConstants.CHAT_MODEL_VALUE,
                            PromptConstants.PROVIDER, "groq"
                    );
                    knowledgeBase.setVectorMetaData(JsonUtils.convertToJSON(vectorMetaData));
                    knowledgeBase.setDocId("[]");
                    knowledgeBase.setCollectionName(dto.getCollectionName());
                    log.debug("Collection metadata set for collection: {}", dto.getCollectionName());
                }
                default ->
                        throw new UnsupportedOperationException(String.format("provided type %s is invalid", dto.getType()));
            }

            knowledgeBase.setCreatedTime(new Date());
            knowledgeBase.setTag(dto.getTags());
            KnowledgeBase saved = knowledgeDao.save(knowledgeBase);
            log.debug("saved successfully knowledge data");
            result.put("id", saved.getId().toString());
            result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("Error while saving knowledge base: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * Reads content from a DOCX file and converts it into Document objects.
     * This method processes the content by page and creates Document objects
     * for each page, which can then be stored in the vector database.
     *
     * @param byteArrayResource The DOCX file content as a ByteArrayResource
     * @return A list of Document objects containing the extracted content
     */
    private List<Document> getContentFromDocx(ByteArrayResource byteArrayResource) {
        log.debug("Starting to read DOCX");
        try (InputStream inputStream = byteArrayResource.getInputStream()) {
            XWPFDocument document = new XWPFDocument(inputStream);
            StringBuilder fullText = new StringBuilder();
            fullText = getFullText(document, fullText);
            log.debug("Successfully read DOCX content");
            return chunkAndTokenize(fullText.toString(), null);
        } catch (IOException e) {
            throw new BusinessException("Error reading DOCX file: " + e.getMessage(), e);
        }
    }

    private static StringBuilder getFullText(XWPFDocument document, StringBuilder fullText) {
        log.debug("Starting to extract text from DOCX");
        try {
            for (IBodyElement element : document.getBodyElements()) {
                if (element instanceof XWPFParagraph paragraph) {
                    appendParagraphText(paragraph, fullText);
                } else if (element instanceof XWPFTable table) {
                    appendTableText(table, fullText);
                }
            }
            log.debug("Successfully extracted text from DOCX");
            return fullText;
        } catch (Exception e) {
            throw new BusinessException("Failed to extract text from DOCX", e);
        }
    }

    private static void appendParagraphText(XWPFParagraph paragraph, StringBuilder fullText) {
        if (isPageBreak(paragraph)) {
            fullText.append("\n");
            return;
        }
        String text = paragraph.getText().trim();
        if (!text.isEmpty()) {
            fullText.append(text).append("\n");
        }
    }

    private static void appendTableText(XWPFTable table, StringBuilder fullText) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                String cellText = cell.getText().trim();
                if (!cellText.isEmpty()) {
                    fullText.append(cellText).append(" | ");
                }
            }
            fullText.append("\n");
        }
    }

    // Dummy page break check – customize based on your formatting logic
    private static boolean isPageBreak(XWPFParagraph paragraph) {
        return paragraph.getStyle() != null && paragraph.getStyle().toLowerCase().contains("heading");
    }

    /**
     * Extracts file names from a list of MultipartFiles.
     * This method is used to collect the names of uploaded files for metadata purposes.
     *
     * @param files A list of MultipartFiles
     * @return A set of file names extracted from the provided files
     */
    private static Set<String> extractFileNames(List<MultipartFile> files) {
        return files.stream()
                .map(MultipartFile::getOriginalFilename) // Extract the file name
                .filter(Objects::nonNull) // Ensure no null values
                .collect(Collectors.toSet()); // Collect into a list
    }

    /**
     * Splits a text input into smaller chunks of specified size.
     * This method is used for breaking down large text content into manageable pieces
     * for processing and storage in the vector database.
     *
     * @param input The text content to be chunked
     * @param chunkSize The maximum size of each chunk in characters
     * @return A list of text chunks
     */
    private static List<String> doChunk(String input, int chunkSize) {
        List<String> chunks = new ArrayList<>();
        if (input == null || input.isEmpty()) {
            return chunks;
        }
        int length = input.length();
        for (int i = 0; i < length; i += chunkSize) {
            chunks.add(input.substring(i, Math.min(length, i + chunkSize)));
        }
        return chunks;
    }

    /**
     * Extracts text content from a PDF file and converts it into Document objects.
     * Uses Spring AI's PDF document reader to process the PDF and extract text by pages.
     * Each page is converted into a separate Document object for vector storage.
     *
     * @param resource The PDF file content as a ByteArrayResource
     * @return A list of Document objects containing the extracted text content
     * @throws RuntimeException if there's an error reading or processing the PDF
     */
    private List<Document> getContentFromPdf(ByteArrayResource resource){
        log.debug("Starting to read PDF content");
        try (PDDocument document = Loader.loadPDF(resource.getByteArray())) {
            PDFTextStripper stripper = new PDFTextStripper();
            String text = stripper.getText(document);
            log.debug("Successfully extracted text from PDF");
            return chunkAndTokenize(text, null);
        } catch (Exception e) {
            throw new BusinessException("Failed to extract text from PDF: " + e.getMessage(), e);
        }
    }

    private List<Document> chunkAndTokenize(String fullText, String metadata) {
        log.debug("Starting to chunk and tokenize content");
        // 1. Chunk by ~2000 characters
        List<String> charChunks = doChunk(fullText, DEFAULT_CHUNK_SIZE);

        // 2. Recursively split each by 400-token max
        return charChunks.parallelStream()
                .flatMap(chunk -> splitRecursively(chunk, MAX_TOKENS_PER_CHUNK).stream())
                .map(chunk -> metadata != null ? createDoc(chunk, metadata) : new Document(chunk))
                .toList();
    }

    private static int findNearestWhitespace(String text, int around) {
        int left = around;
        int right = around;

        while (left > 0 || right < text.length()) {
            if (left > 0 && Character.isWhitespace(text.charAt(left))) {
                return left;
            }
            if (right < text.length() && Character.isWhitespace(text.charAt(right))) {
                return right;
            }
            left--;
            right++;
        }

        log.debug("Successfully found nearest whitespace");
        return around; // fallback
    }

    private List<String> splitRecursively(String content, int maxTokens) {
        List<String> results = new ArrayList<>();
        int tokenCount = chunkCounter.countChunks(content);

        if (tokenCount <= maxTokens) {
            results.add(content.trim());
            return results;
        }

        // Split at nearest whitespace around the middle
        int mid = content.length() / MID_DIVISOR;
        int splitIndex = findNearestWhitespace(content, mid);
        if (splitIndex <= 0 || splitIndex >= content.length()) {
            // Fallback: can't split properly
            results.add(content.trim());
            return results;
        }

        String firstHalf = content.substring(0, splitIndex).trim();
        String secondHalf = content.substring(splitIndex).trim();

        // Recursively split each half
        results.addAll(splitRecursively(firstHalf, maxTokens));
        results.addAll(splitRecursively(secondHalf, maxTokens));
        log.debug("Content and tokens split successfully");
        return results;
    }




    /**
     * Converts a MultipartFile to a ByteArrayResource.
     * This utility method is used to transform uploaded files into a format
     * that can be processed by document readers and content extractors.
     *
     * @param files The MultipartFile to convert
     * @return A ByteArrayResource containing the file's bytes, or null if conversion fails
     */
    private static ByteArrayResource getByteArrayResources(MultipartFile files) {
        try {
            log.debug("Converting file to ByteArrayResource: {}", files.getOriginalFilename());
            return new ByteArrayResource(files.getBytes());
        } catch (IOException e) {
            log.error("Error converting file: {}", files.getOriginalFilename(), e);
            return null;
        }
    }


    /**
     * Counts the number of knowledge base entries that match the provided filter criteria.
     *
     * @param filter the filter criteria used to count knowledge base entries
     * @return the count of knowledge base entries that match the filter
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(KnowledgeBase.class, filter);
    }

    /**
     * Searches for knowledge base entries based on the given filter and pagination parameters.
     *
     * @param filter    the search filter criteria
     * @param offset    the offset for pagination
     * @param size      the size (limit) for pagination
     * @param orderBy   the field to order the results by
     * @param orderType the type of sorting (ascending or descending)
     * @return a list of knowledge base entries matching the search criteria
     * @throws BusinessException if an error occurs while searching
     */
    @Override
    public List<KnowledgeBaseResponseDto> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        try {
            List<KnowledgeBase> knowledgeBases = customFilter.searchByFilter(
                    KnowledgeBase.class, filter, orderBy, orderType, offset, size
            );
            return KnowledgeConverter.getKnowledgeBasesDtoList(knowledgeBases);
        } catch (Exception e) {
            log.error("Error message : {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Updates an existing document's content in the knowledge base.
     * The method processes the new file (if provided), extracts its content,
     * and updates the document's metadata and content in the vector store.
     *
     * @param id  the ID of the knowledge base document to update
     * @param dto the DTO containing the updated document details
     * @return a map with the result of the update operation ("success" or "failed")
     * @throws BusinessException if the document cannot be updated or processed
     */
    @Override
    public Map<String, String> updateDocument(Integer id, DocumentRequestDto dto) throws JsonProcessingException {
        log.debug("Inside @class KnowledgeBaseServiceImpl @method updateDocument");
        Map<String, String> result = new HashMap<>();
        List<List<Document>> documents = new ArrayList<>();
        List<String> s3StorageFileNames = new ArrayList<>();

        KnowledgeBase existingKnowledgeBase = knowledgeDao.findById(id)
                .orElseThrow(() -> new BusinessException("KnowledgeBase record not found for ID: " + id));


        try {
            mapToEntity(dto, existingKnowledgeBase);
            String oldDocIds = existingKnowledgeBase.getDocId();
            String oldFilter = existingKnowledgeBase.getFilter();

            switch (dto.getType()) {
                case "DOCUMENT" -> {
                    List<MultipartFile> files = dto.getFile();
                    Set<String> fileNames = extractFileNames(files);
                    for (MultipartFile file : files) {
                        fileNames.add(file.getOriginalFilename());
                        List<Document> content = extractContentFromFile(file);
                        if (content != null && !content.isEmpty()) {
                            documents.add(content);
                        }
                    }

                    existingKnowledgeBase.setFileName(String.join(", ", fileNames));
                    VectorResponseDto vectorResponse = saveInVectorFromDocReader(documents);
                    log.info("Going to update file on s3service");
                    s3StorageFileNames = s3Service.uploadFileToS3(files,vectorResponse.getFilter(),existingKnowledgeBase.getS3FileNames());
                    existingKnowledgeBase.setS3FileNames(JsonUtils.convertToJSON(s3StorageFileNames));
                    existingKnowledgeBase.setFilter(vectorResponse.getFilter());
                    existingKnowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
                    existingKnowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));

                    log.debug("Updated document content for ID: {}", id);
                }
                case "SQL" -> {
                    List<String> tableNames = dto.getTables();
                    List<String> docIds = new ArrayList<>();
                    for (String tableName : tableNames) {
                        Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, tableName);
                        String tableSchema = apiService.getTableSchema(dto.getIntegration(), tableName);
                        Document document = new Document(tableSchema);
                        document.getMetadata().putAll(docMetaData);
                        docIds.add(document.getId());
                        vectorStore.accept(List.of(document));
                        log.debug("Document is successfully save in vector. table : {}", tableName);
                    }
                    existingKnowledgeBase.setDocId(JsonUtils.convertToJSON(docIds));
                    existingKnowledgeBase.setDescription(String.format("""
                            Tool is designed to assist an LLM in constructing valid SQL queries by providing the schema details of a specified database table.
                            Given a table name as input, this tool retrieves and returns its schema, including column names, data types, constraints, and other relevant metadata.
                            It contains schema for following tables:
                            %s
                            """, tableNames));
                    existingKnowledgeBase.setIntegration(dto.getIntegration());
                    existingKnowledgeBase.setTables(JsonUtils.convertToJSON(tableNames));
                }
                case "WEBSITE" -> {
                    processWebsiteData(dto, existingKnowledgeBase);
                    log.debug("Updated website details for ID: {}", id);
                }
                case "COLLECTION" -> {
                    Map<String, String> vectorMetaData = Map.of(
                            PromptConstants.VECTOR_DATABASE, PromptConstants.MILVUS,
                            PromptConstants.DATABASE_NAME, databaseName,
                            PromptConstants.COLLECTION_NAME, dto.getCollectionName(),
                            PromptConstants.EMBEDDING_MODEL_KEY, PromptConstants.EMBEDDING_MODEL,
                            PromptConstants.CHAT_MODEL_KEY, PromptConstants.CHAT_MODEL_VALUE,
                            PromptConstants.PROVIDER, "groq");
                    existingKnowledgeBase.setVectorMetaData(JsonUtils.convertToJSON(vectorMetaData));
                    existingKnowledgeBase.setDocId("[]");
                    existingKnowledgeBase.setCollectionName(dto.getCollectionName());
                    existingKnowledgeBase.setTag(dto.getTags());
                    log.debug("Updated collection metadata");
                }
                default ->
                        throw new UnsupportedOperationException(String.format("provided type %s is invalid", dto.getType()));
            }


            // Delete old vector data before saving the new file content
            Filter.Expression expression = new Filter.Expression(Filter.ExpressionType.EQ, new Filter.Key(KNOWLEDGE_BASE_FILTER_KEY), new Filter.Value(oldFilter));
            for (int i = 0; i < PromptConstants.VECTOR_STORE_DELETION_BATCH_SIZE; i++) {
                vectorStore.delete(expression);
            }
            List<String> oldDocIdList = JsonUtils.convertJsonToList(oldDocIds, String.class);
            vectorStore.delete(oldDocIdList);

            knowledgeDao.save(existingKnowledgeBase);
            log.debug("Updated successfully knowledge data");
            result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("Error while updating knowledge base: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * Extracts content from an uploaded file based on its file type.
     * This method determines the file type from the extension and delegates to the appropriate
     * extraction method for PDF or DOCX files.
     *
     * @param file The uploaded file to extract content from
     * @return A list of Document objects containing the extracted content
     * @throws IOException If there's an error reading the file
     * @throws IllegalArgumentException If the file type is not supported
     */
    private List<Document> extractContentFromFile(MultipartFile file) throws IOException {
        log.debug("Inside method extractContentFromFile: {}", file.getOriginalFilename());
        String fileType = getFileExtension(file.getOriginalFilename());

        return switch (fileType.toLowerCase()) {
            case "pdf" -> extractContentFromPDF(file);
            case "docx" -> extractContentFromWord(file);
            case "xlsx" , "xls" -> extractContentFromExcel(file);
            default -> throw new IllegalArgumentException("Unsupported file type: " + fileType);
        };
    }

    private List<Document> extractContentFromExcel(MultipartFile file) throws IOException {
        log.debug("Extracting content from Excel file: {}", file.getOriginalFilename());

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            // Process sheets in parallel
            return IntStream.range(0, workbook.getNumberOfSheets())
                    .parallel()
                    .mapToObj(i -> processSheet(workbook.getSheetAt(i)))
                    .flatMap(List::stream)
                    .toList(); // Java 16+, use .collect(Collectors.toList()) if older
        } catch (Exception e) {
            log.error("Failed to extract content from Excel file: {}", file.getOriginalFilename(), e);
            throw new IOException("Error reading Excel file", e);
        }
    }

    private List<Document> processSheet(Sheet sheet) {
        log.debug("Processing sheet: {}", sheet.getSheetName());
        String sheetName = sheet.getSheetName();
        List<List<String>> rows = new ArrayList<>();
        List<Integer> maxColWidths = new ArrayList<>();

        for (Row row : sheet) {
            List<String> cellValues = new ArrayList<>();
            for (int colIndex = 0; colIndex < row.getLastCellNum(); colIndex++) {
                Cell cell = row.getCell(colIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                String value = getFormattedCellValue(cell);
                cellValues.add(value);

                while (maxColWidths.size() <= colIndex) {
                    maxColWidths.add(0);
                }
                maxColWidths.set(colIndex, Math.max(maxColWidths.get(colIndex), value.length()));
            }
            rows.add(cellValues);
        }

        // Format all text in one go
        StringBuilder fullText = new StringBuilder();
        for (List<String> row : rows) {
            fullText.append(formatRow(row, maxColWidths)).append("\n");
        }

        // First chunk by 2000 characters
        List<String> charChunks = doChunk(fullText.toString(), DEFAULT_CHUNK_SIZE);

        log.debug("Successfully chunked content from Excel file: {}", sheetName);

        // Then split each by 400-token max
        return charChunks.parallelStream()
                .flatMap(charChunk -> splitRecursively(charChunk, MAX_TOKENS_PER_CHUNK).stream())
                .map(chunk -> createDoc(chunk, sheetName))
                .toList();
    }

    private String getFormattedCellValue(Cell cell) {
        if (cell == null) return "";

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().trim();
            case NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    yield cell.getDateCellValue().toString();
                }
                double numericValue = cell.getNumericCellValue();
                boolean isInteger = numericValue == Math.floor(numericValue);
                yield isInteger ? String.format("%.0f", numericValue) : String.valueOf(numericValue);
            }
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> {
                try {
                    FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
                    yield getFormattedCellValue(evaluator.evaluateInCell(cell));
                } catch (Exception e) {
                    yield "FORMULA_ERROR";
                }
            }
            case BLANK, _NONE, ERROR -> "";
        };
    }

    private String formatRow(List<String> row, List<Integer> maxColWidths) {
        StringJoiner joiner = new StringJoiner(" | ");
        for (int i = 0; i < row.size(); i++) {
            int width = Math.max(1, maxColWidths.get(i)); // Ensure minimum width
            String format = "%-" + width + "s";
            String padded = String.format(format, row.get(i));
            joiner.add(padded);
        }
        return joiner.toString();
    }

    private static Document createDoc(String text, String sheetName) {
        Document doc = new Document(text);
        doc.getMetadata().put("sheetname", sheetName);
        return doc;
    }


    /**
     * Extracts content from a Word document file.
     * Converts the MultipartFile to a ByteArrayResource and delegates to the
     * getContentFromDocx method for actual content extraction.
     *
     * @param file The Word document file to extract content from
     * @return A list of Document objects containing the extracted content
     * @throws IOException If there's an error reading the file
     */
    private List<Document> extractContentFromWord(MultipartFile file) throws IOException {
        ByteArrayResource resource = getByteArrayResources(file);
        log.debug("Inside method extractContentFromWord");
        return getContentFromDocx(resource);
    }


    /**
     * Extracts content from a PDF file.
     * Converts the MultipartFile to a ByteArrayResource and delegates to the
     * getContentFromPdf method for actual content extraction.
     *
     * @param file The PDF file to extract content from
     * @return A list of Document objects containing the extracted content
     * @throws IOException If there's an error reading the file
     */
    private List<Document> extractContentFromPDF(MultipartFile file) throws IOException {
        ByteArrayResource resource = getByteArrayResources(file);
        log.debug("Inside method extractContentFromPDF");
        return getContentFromPdf(resource);
    }

    /**
     * Marks a knowledge base entry as deleted (soft delete).
     * The entry will not be permanently removed from the database but its `deleted` flag is set to `true`.
     *
     * @param id the ID of the knowledge base entry to soft delete
     * @return a map with the result of the operation ("success" or "failed")
     */
    @Override
    public Map<String, String> softDelete(Integer id) {
        Map<String, String> result = new HashMap<>();
        Optional<KnowledgeBase> knowledgeBase = knowledgeDao.findById(id);
        if (knowledgeBase.isPresent()) {
            KnowledgeBase knowledgeBase1 = knowledgeBase.get();
            knowledgeBase1.setDeleted(true);
            knowledgeDao.save(knowledgeBase1);
            result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
        } else {
            result.put(PromptConstants.RESULT, PromptConstants.FAILED);
        }
        return result;
    }

    /**
     * Retrieves multiple knowledge base entries by their IDs.
     *
     * @param ids the list of IDs of the knowledge base entries to retrieve
     * @return a list of DTOs containing the knowledge base details
     */
    @Override
    public List<KnowledgeBaseSdkDto> getKnowledgeBaseByIds(List<Integer> ids) {
        List<KnowledgeBase> knowledgeBases = knowledgeDao.findAllById(ids);
        return SdkUtils.getKnowledgeBaseSdkDtoList(knowledgeBases);

    }

    /**
     * Maps document request data to a KnowledgeBase entity.
     *
     * @param dto           The document request data
     * @param knowledgeBase The knowledge base entity to update
     * @throws JsonProcessingException if vector metadata cannot be processed
     */
    private void mapToEntity(DocumentRequestDto dto, KnowledgeBase knowledgeBase) throws JsonProcessingException {
       log.debug("Inside method mapToEntity");
        try {
            knowledgeBase.setIsContext(false);
            knowledgeBase.setType(dto.getType());
            knowledgeBase.setName(dto.getName());
            knowledgeBase.setDocType(dto.getDocType());
            knowledgeBase.setDescription(dto.getDescription());
            knowledgeBase.setSimilarityThreshold(dto.getSimilarityThreshold());
            knowledgeBase.setTopK(dto.getTopK());
            Map<String, String> vectorMetaData = Map.of(
                    PromptConstants.VECTOR_DATABASE, PromptConstants.MILVUS,
                    PromptConstants.DATABASE_NAME, databaseName,
                    PromptConstants.COLLECTION_NAME, collectionName,
                    PromptConstants.EMBEDDING_MODEL_KEY, PromptConstants.EMBEDDING_MODEL,
                    PromptConstants.CHAT_MODEL_KEY, PromptConstants.CHAT_MODEL_VALUE,
                    PromptConstants.PROVIDER, "groq");
            log.debug("Successfully load vectorMetaData");
            if (dto.getDeleted() == null) {
                knowledgeBase.setDeleted(false);
            } else {
                knowledgeBase.setDeleted(dto.getDeleted());
            }
            knowledgeBase.setVectorMetaData(JsonUtils.convertToJSON(vectorMetaData));
            knowledgeBase.setReturnDirect(false);
            knowledgeBase.setModifiedTime(new Date());
        } catch (Exception e) {
            throw new BusinessException("Error in map to entity" + e.getMessage() + e);
        }
        log.debug("Mapped document request data to knowledge base entity");
    }

    /**
     * Saves text content to the vector store for semantic search.
     * This method processes text content by chunking it into smaller pieces,
     * adds metadata to each chunk, and stores them in the vector database.
     *
     * @param contents A list of text content strings to save in the vector store
     * @return A VectorResponseDto containing filter, metadata, and document IDs
     */
    public VectorResponseDto saveInVector(List<String> contents) {
        String filter = UUID.randomUUID().toString();
        Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, filter);
        List<String> docIds = new ArrayList<>();

        for (String content : contents) {
            List<String> chunks = doChunk(content, LARGE_CHUNK_SIZE);
            List<Document> documents = new ArrayList<>();

            for (String chunk : chunks) {
                Document document = new Document(chunk);
                document.getMetadata().putAll(docMetaData);
                documents.add(document);
                docIds.add(document.getId());
            }
            vectorStore.accept(documents);
        }
        log.debug("Document is successfully save in vector");
        return new VectorResponseDto(filter, docMetaData, docIds);
    }

    /**
     * Saves document content from a document reader to the vector store.
     * This method processes documents that have been extracted from files like PDFs or DOCXs,
     * adds metadata, and stores them in the vector database for semantic search.
     *
     * @param documentList A list of document lists containing the extracted content
     * @return A VectorResponseDto containing filter, metadata, and document IDs
     */
    public VectorResponseDto saveInVectorFromDocReader(List<List<Document>> documentList) {
        String filter = UUID.randomUUID().toString();
        Map<String, Object> docMetaData = Map.of(KNOWLEDGE_BASE_FILTER_KEY, filter);
        List<String> docIds = new ArrayList<>();
        processDocuments(documentList, docMetaData, docIds);
        log.debug("Successfully save in vector");
        return new VectorResponseDto(filter, docMetaData, docIds);
    }

    /**
     * Saves content from a CSV file to the vector store.
     * This method processes text content extracted from CSV files,
     * adds custom agent metadata, and stores the content in batches
     * for efficient vector database operations.
     *
     * @param contents A list of text content strings from the CSV file
     */
    public void saveInVectorFromCsv(List<String> contents) {
        Map<String, Object> docMetaData = Map.of(CUSTOM_AGENT_FILTER_KEY, CUSTOM_AGENT_FILTER);

        List<Document> batch = new ArrayList<>();

        for (String content : contents) {
            Document doc = new Document(content);
            doc.getMetadata().putAll(docMetaData);
            batch.add(doc);

            if (batch.size() == BATCH_SIZE) { //save BATCH_SIZE records at a time
                log.debug("saving batch of {} records", BATCH_SIZE);
                vectorStore.accept(new ArrayList<>(batch));
                batch.clear();
            }
        }

        // Handle remaining documents
        if (!batch.isEmpty()) {
            vectorStore.accept(batch);
        }

        log.debug("successfully save csv contents in vector");

    }

    /**
     * Processes a list of document lists for storage in the vector database.
     * This method handles batch processing of documents, adding metadata and collecting document IDs.
     * Documents are processed in batches to optimize vector store operations.
     *
     * @param documentList A list of document lists to process
     * @param docMetaData Metadata to be added to each document
     * @param docIds A list to collect the IDs of processed documents
     */
    private void processDocuments(List<List<Document>> documentList, Map<String, Object> docMetaData, List<String> docIds) {
        List<Document> batch = new ArrayList<>();
        int batchSize = BATCH_SIZE;
        for (List<Document> documents : documentList) {
            for (Document document : documents) {
                document.getMetadata().putAll(docMetaData);
                batch.add(document);
                docIds.add(document.getId());
                // If the batch size reaches the limit, process it
                if (batch.size() == batchSize) {
                    vectorStore.accept(new ArrayList<>(batch)); // Process the batch
                    batch.clear(); // Clear the batch for the next set
                }
            }
        }
        // Process any remaining documents that didn't complete a full batch
        if (!batch.isEmpty()) {
            vectorStore.accept(batch);
        }
        log.debug("successfully Processed documents");
    }

    /**
     * Updates tags for a knowledge base entry by ID.
     *
     * @param id   The ID of the knowledge base entry
     * @param tags The tags to update
     * @return Map containing the result of the update operation
     */

    @Override
    public Map<String, String> updateTagById(Integer id, Map<String, String> tags) {
        String newTag = tags.get("tags");
        log.debug("inside @method updateTagById. @param  : id -> {} tags : {}", id, newTag);
        KnowledgeBase knowledgeBase = knowledgeDao.findById(id).orElseThrow(() -> new BusinessException("knowledge base is not found for id : " + id));

        try {
            knowledgeBase.setTag(newTag);
            knowledgeDao.save(knowledgeBase);
            return Map.of(APIConstants.RESULT, APIConstants.SUCCESS);
        } catch (Exception e) {
            log.error("error while updating tag of knowledge base : {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage(), e);
        }
    }

    /**
     * Retrieves the names of tables associated with a given name.
     *
     * @param name The name to search for table names
     * @return List of table names
     */
    @Override
    public List<String> getTablesName(String name) {
        return apiService.getTablesName(name);
    }


    /**
     * Extracts file extension from filename.
     *
     * @param filename The name of the file
     * @return The file extension
     * @throws IllegalArgumentException if filename is invalid
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            throw new IllegalArgumentException("Invalid file name: " + filename);
        }
        return filename.substring(filename.lastIndexOf('.') + 1);
    }

     /**
     * Checks if a website URL already exists in the knowledge base.
     * This method queries the database to find any knowledge base entries with the same URL
     * that are not marked as deleted. If found, it returns their IDs.
     *
     * @param webSiteUrl The website URL to check for existence
     * @return A map containing the existence status ("exists" boolean) and IDs of matching entries if found
     * @throws BusinessException if there's an error checking the website URL existence
     */
    @Override
    public Map<String, Object> existsWebsiteUrl(String webSiteUrl) {
        log.debug("Checking existence of website URL: {}", webSiteUrl);
        Map<String, Object> result = new HashMap<>();
        try {
            List<KnowledgeBase> knowledgeBaseList = knowledgeDao.existsWebsiteUrl(webSiteUrl);
            boolean exists = !knowledgeBaseList.isEmpty();
            result.put("exists", exists);
            if (exists) {
                List<Integer> ids = knowledgeBaseList.stream()
                        .map(KnowledgeBase::getId)
                        .toList();
                result.put("ids", ids);
                log.info("Website URL: {} exists with IDs: {}", webSiteUrl, ids);
            }
        } catch (Exception e) {
            log.error("Error while checking website URL: {} - {}", webSiteUrl, e.getMessage(), e);
            throw new BusinessException("Failed to check website URL existence: " + e.getMessage());
        }
        return result;
    }

    /**
     * Saves raw text content to the knowledge base.
     * This method takes raw text content from the DTO, saves it to the vector store,
     * and creates a knowledge base entry with the content metadata.
     *
     * @param dto The DTO containing the content to save and metadata
     * @return A map with the result of the save operation and the created knowledge base ID
     * @throws BusinessException if there's an error processing the content or saving to the database
     */
    @Override
    public Map<String, String> saveContent(DocumentRequestDto dto) {
        log.debug("Inside @class KnowledgeBaseServiceImpl @method saveContent");
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        Map<String, String> result = new HashMap<>();
        try {
            mapToEntity(dto, knowledgeBase);
            VectorResponseDto vectorResponse = saveInVector(List.of(dto.getContent()));
            knowledgeBase.setFilter(vectorResponse.getFilter());
            knowledgeBase.setDocMetaData(JsonUtils.convertToJSON(vectorResponse.getMetadata()));
            knowledgeBase.setDocId(JsonUtils.convertToJSON(vectorResponse.getDocIds()));
            knowledgeBase.setTag(dto.getTags());
            knowledgeBase.setCreatedTime(new Date());
            knowledgeBase = knowledgeDao.save(knowledgeBase);
            log.debug("method saveContent , saved successfully knowledge data");
             result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
            result.put("id", String.valueOf(knowledgeBase.getId()));
            return result;
        } catch (JsonProcessingException e) {
            log.error("Error while saving knowledge base: {}", e.getMessage(), e);
            throw new BusinessException("Error while saveContent knowledge base " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<String> milvusImportCsv(MultipartFile file) {
        log.debug("Inside @method milvusImportCsv");
        try (CSVReader csvReader = new CSVReader(new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {
            List<String[]> rows = csvReader.readAll();
            if (rows.isEmpty()) {
                return ResponseEntity.badRequest().body("CSV file is empty.");
            }
            int contentIndex = Arrays.asList(rows.get(0)).indexOf("content"); // Find "content" column index
            if (contentIndex == -1) {
                return ResponseEntity.badRequest().body("CSV file does not contain 'content' column.");
            }
            List<String> contents = rows.stream()
                    .skip(1) // Skip header row
                    .map(row -> row[contentIndex])
                    .filter(content -> content != null && !content.isEmpty())
                    .toList(); // Use `toList()` for an immutable list (Java 16+)

            if (contents.isEmpty()) {
                return ResponseEntity.badRequest().body("No valid content found in CSV.");
            }

            log.debug("Successfully extracted {} content records from CSV.", contents.size());
            // Delete old records before saving new content
            Filter.Expression expression = new Filter.Expression(Filter.ExpressionType.EQ, new Filter.Key(CUSTOM_AGENT_FILTER_KEY), new Filter.Value(CUSTOM_AGENT_FILTER));
            for (int i = 0; i < PromptConstants.CUSTOM_AGENT_DELETION_BATCH_SIZE; i++) {
                vectorStore.delete(expression);
            }
            saveInVectorFromCsv(contents);
            return ResponseEntity.ok("CSV data imported successfully.");

        } catch (Exception e) {
            log.error("Error processing CSV file: ", e);
            return ResponseEntity.internalServerError().body("Failed to process CSV file.");
        }
    }

    /**
     * Generates an answer to a user question using the specified knowledge base.
     * This method retrieves the knowledge base by name, extracts relevant information from it,
     * and uses the vector store to find the most relevant content to answer the question.
     * The answer is generated using the configured AI model.
     *
     * @param knowledgeBaseName The name of the knowledge base to use for answering
     * @param userQuestion The user's question to be answered
     * @return A map containing the generated answer and related metadata
     * @throws NoSuchElementException if the knowledge base is not found
     * @throws BusinessException if there's an error generating the answer
     */
    @Override
    public Map<String, String> getAnswer(String knowledgeBaseName, String userQuestion) {
        log.debug("Inside @class KnowledgeBaseServiceImpl @method getAnswer with knowledgeBaseName: {}", knowledgeBaseName);
        try {
            // 1. Retrieve knowledge base
            KnowledgeBase knowledgeBase = Optional.ofNullable(knowledgeDao.findByName(knowledgeBaseName))
                    .orElseThrow(() -> new NoSuchElementException("Knowledge base not found: " + knowledgeBaseName));

            // 2. Parse vector metadata
            Map<String, String> vectorMetaData = JsonUtils.convertJsonToObject(knowledgeBase.getVectorMetaData(), Map.class);
            String provider = vectorMetaData.get(PromptConstants.PROVIDER);
            String model = vectorMetaData.get(PromptConstants.CHAT_MODEL_KEY);


            OpenAiChatOptions chatOptions = OpenAiChatOptions.builder().model(model).build();
            log.debug("Chat options initialized for provider: {} and model: {}", provider, model);

            // 4. Build search request
            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(knowledgeBase.getSimilarityThreshold())
                    .topK(knowledgeBase.getTopK())
                    .build();
            log.debug("Search request initialized with topK: {} and threshold: {}", knowledgeBase.getTopK(), knowledgeBase.getSimilarityThreshold());

            // 5. Generate answer
            ChatResponse chatResponse = ChatClient.builder(inferenceManager.getChatModelByProvider(provider))
                    .build()
                    .prompt()
                    .user(userQuestion)
                    .options(chatOptions)
                    .advisors(new QuestionAnswerAdvisor(vectorStore, searchRequest))
                    .call()
                    .chatResponse();

            String answer = chatResponse.getResult().getOutput().getText();
            log.info("Successfully generated answer for question ---> {}  ", userQuestion);

            return Map.of("answer", answer);

        } catch (Exception e) {
            log.error("Error in getAnswer: {}", e.getMessage(), e);
            throw new BusinessException("Failed to process request due to an error: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<Resource> exportKnowledgeBases(List<Integer> ids) {
        log.debug("Inside method exportKnowledgeBases");
        List<KnowledgeBase> knowledgeBaseList = knowledgeDao.findAllById(ids);
        knowledgeBaseList.removeIf(kb -> !Boolean.FALSE.equals(kb.getDeleted()));
        return getResponse("knowledge-bases", knowledgeBaseList);
    }

    private static ResponseEntity<Resource> getResponse(String fileName, List<KnowledgeBase> knowledgeBaseList) {
        log.debug("Inside method getResponse for KnowledgeBase export");
        List<Function<KnowledgeBase, Object>> fieldExtractors = getCombinedFunctionList();
        log.debug("Going to export knowledge bases as csv");
        return CSVUtils.exportCSV(knowledgeBaseList, csvColumnHeader, fileName, fieldExtractors);
    }

    private static List<Function<KnowledgeBase, Object>> getCombinedFunctionList() {
        try {
            log.debug("Inside method getCombinedFunctionList");
            List<Function<KnowledgeBase, Object>> combined = new ArrayList<>();
            combined.addAll(getFunctionListPart1());
            combined.addAll(getFunctionListPart2());
            log.debug("Successfully combined function lists");
            return combined;
        } catch (Exception e) {
            throw new BusinessException("Error in getCombinedFunctionList: " + e.getMessage(), e);
        }
    }

    private static List<Function<KnowledgeBase, Object>> getFunctionListPart1() {
        log.debug("Inside method getFunctionListPart1");
        return List.of(
                KnowledgeBase::getName,
                KnowledgeBase::getDescription,
                KnowledgeBase::getVectorMetaData,
                KnowledgeBase::getTopK,
                KnowledgeBase::getSimilarityThreshold,
                KnowledgeBase::getType,
                kb -> kb.getWebSiteUrl() == null ? "NULL" : kb.getWebSiteUrl(),
                kb -> kb.getFileName() == null ? "NULL" : kb.getFileName(),
                kb -> kb.getWebsiteTaskStatus() == null ? "NULL" : kb.getWebsiteTaskStatus(),
                kb -> kb.getWebsiteTaskId() == null ? "NULL" : kb.getWebsiteTaskId(),
                kb -> kb.getWebsiteTaskError() == null ? "NULL" : kb.getWebsiteTaskError());
    }

    private static List<Function<KnowledgeBase, Object>> getFunctionListPart2() {
        log.debug("Inside method getFunctionListPart2");
        return List.of(
                kb -> kb.getCollectionName() == null ? "NULL" : kb.getCollectionName(),
                kb -> kb.getDocType() == null ? "NULL" : kb.getDocType(),
                kb -> kb.getDocId() == null ? "NULL" : kb.getDocId(),
                kb -> kb.getFilter() == null ? "NULL" : kb.getFilter(),
                kb -> kb.getDocMetaData() == null ? "NULL" : kb.getDocMetaData(),
                KnowledgeBase::getIsContext,
                kb -> kb.getTables() == null ? "NULL" : kb.getTables(),
                kb -> kb.getIntegration() == null ? "NULL" : kb.getIntegration(),
                KnowledgeBase::getReturnDirect
        );
    }


    @Override
    public ResponseEntity<Resource> importKnowledgeBases(MultipartFile file) {
        log.debug("Inside importKnowledgeBases method");
        int maxRecords = MAX_IMPORT_RECORDS;
        try {
            Function<CSVRecord, KnowledgeBase> recordTransformer = csvRecord -> {
                KnowledgeBase knowledgeBase = new KnowledgeBase();

                knowledgeBase.setName(csvRecord.get("Name"));
                knowledgeBase.setDescription(csvRecord.get("Description"));
                knowledgeBase.setVectorMetaData(csvRecord.get("VectorMetaData"));
                knowledgeBase.setTopK(Integer.valueOf(csvRecord.get("TopK")));
                knowledgeBase.setSimilarityThreshold(Double.valueOf(csvRecord.get("SimilarityThreshold")));
                knowledgeBase.setType(csvRecord.get("Type"));
                knowledgeBase.setWebSiteUrl(csvRecord.get("webSiteUrl"));
                knowledgeBase.setFileName(csvRecord.get("fileName"));
                knowledgeBase.setWebsiteTaskStatus(csvRecord.get("websiteTaskStatus"));
                knowledgeBase.setWebsiteTaskId(csvRecord.get("websiteTaskId"));
                knowledgeBase.setWebsiteTaskError(csvRecord.get("websiteTaskError"));
                knowledgeBase.setCollectionName(csvRecord.get(PromptConstants.COLLECTION_NAME));
                knowledgeBase.setDocType(csvRecord.get("docType"));
                knowledgeBase.setDocId(csvRecord.get("docId"));
                knowledgeBase.setFilter(csvRecord.get("filter"));
                knowledgeBase.setDocMetaData(csvRecord.get("docMetaData"));
                knowledgeBase.setIsContext(Boolean.valueOf(csvRecord.get("isContext")));
                knowledgeBase.setTables(csvRecord.get("tables"));
                knowledgeBase.setIntegration(csvRecord.get("integration"));
                knowledgeBase.setReturnDirect(Boolean.valueOf(csvRecord.get("returnDirect")));

                return knowledgeBase;
            };

            String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
            log.info("Importing knowledge bases from file: {}", exportedFileName);
            return CSVUtils.importCSV(file, csvColumnHeader, recordTransformer, this::saveOrUpdateKnowledgeBase, exportedFileName, maxRecords);

        } catch (IOException | BusinessException e) {
            log.error("Error during knowledge base import: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ByteArrayResource(e.getMessage().getBytes()));
        }
    }

    /**
     * Updates the return direct flag for a knowledge base entry.
     * This flag determines whether the knowledge base content should be returned directly
     * without additional processing when used in queries.
     *
     * @param id The ID of the knowledge base entry to update
     * @param returnDirectValue The new value for the return direct flag
     * @return Map containing the result of the update operation
     * @throws BusinessException if the knowledge base entry is not found
     */
    @Override
    public Map<String, String> updateReturnDirect(Integer id, Boolean returnDirectValue) {
        log.debug("Inside @method updateReturnDirect with id: {} and returnDirectValue: {}", id, returnDirectValue);
        try {
            KnowledgeBase knowledgeBase = knowledgeDao.findById(id)
                    .orElseThrow(() -> new BusinessException("Knowledge base not found with id: " + id));
            knowledgeBase.setReturnDirect(returnDirectValue);
            knowledgeDao.save(knowledgeBase);
            log.debug("Successfully updated return direct flag for knowledge base with id: {}", id);
            return Map.of(APIConstants.RESULT, APIConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error while updating return direct flag: {}", e.getMessage(), e);
        }
        return Map.of(APIConstants.RESULT, APIConstants.FAILED);
    }

    /**
     * Saves website-specific knowledge base data.
     * This method handles the common logic for saving website data used by both
     * saveDocument and saveWebSite methods.
     *
     * @param documentDto The document request DTO containing website information
     * @return Map containing the result of the save operation
     */
    @Override
    public Map<String, String> saveWebSite(DocumentRequestDto documentDto) {
        log.debug("Inside @method saveWebSite");
        KnowledgeBase knowledgeBase = new KnowledgeBase();
        Map<String, String> result = new HashMap<>();
        try {
            mapToEntity(documentDto, knowledgeBase);

            // Process website-specific data
            processWebsiteData(documentDto, knowledgeBase);

            // Set common properties
            knowledgeBase.setCreatedTime(new Date());
            knowledgeBase.setTag(documentDto.getTags());

            // Save to database
            KnowledgeBase saved = knowledgeDao.save(knowledgeBase);
            log.debug("Successfully saved website knowledge base data with ID: {}", saved.getId());

            // Prepare result
            result.put("id", saved.getId().toString());
             result.put(PromptConstants.RESULT, PromptConstants.SUCCESS);
            return result;
        } catch (Exception e) {
            log.error("Error while saving website knowledge base: {}", e.getMessage(), e);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * Saves or updates a knowledge base entity in the database.
     * If a knowledge base with the same name already exists, it updates the existing one.
     * Otherwise, it creates a new knowledge base entry.
     *
     * @param knowledgeBase The knowledge base entity to save or update
     * @return The saved or updated knowledge base entity
     */
    private KnowledgeBase saveOrUpdateKnowledgeBase(KnowledgeBase knowledgeBase) {
        log.debug("Saving or updating knowledge base: {}", knowledgeBase.getName());

        Optional<KnowledgeBase> existingKnowledgeBase = Optional.ofNullable(knowledgeDao.findByName(knowledgeBase.getName()));

        if (existingKnowledgeBase.isPresent()) {
            KnowledgeBase existing = existingKnowledgeBase.get();
            mapKnowledgeBase(knowledgeBase, existing);
            existing.setModifiedTime(new Date());
            return knowledgeDao.save(existing);
        } else {
            knowledgeBase.setDeleted(false);
            knowledgeBase.setCreatedTime(new Date());
            return knowledgeDao.save(knowledgeBase);
        }
    }

    /**
     * Processes website-specific data for a knowledge base entity.
     * This method handles the common logic for website data processing used by both
     * saveDocument and saveWebSite methods.
     *
     * @param dto The document request DTO containing website information
     * @param knowledgeBase The knowledge base entity to update with website data
     */
    private void processWebsiteData(DocumentRequestDto dto, KnowledgeBase knowledgeBase) {
        String websiteUrl = dto.getWebsiteUrl();
        String taskId = apiService.triggerCrawl(websiteUrl);
        knowledgeBase.setDocId("[]");
        knowledgeBase.setWebSiteUrl(websiteUrl);
        knowledgeBase.setWebsiteTaskId(taskId);
        knowledgeBase.setWebsiteTaskStatus("PROCESSING");
        log.debug("Triggered crawl for website: {}", websiteUrl);
    }

    /**
     * Maps properties from one KnowledgeBase entity to another.
     *
     * @param source The source KnowledgeBase containing new values
     * @param target The target KnowledgeBase to update
     */
    private void mapKnowledgeBase(KnowledgeBase source, KnowledgeBase target) {
        log.debug("Mapping properties from source to target knowledge base");

        target.setDescription(source.getDescription());
        target.setVectorMetaData(source.getVectorMetaData());
        target.setTopK(source.getTopK());
        target.setSimilarityThreshold(source.getSimilarityThreshold());
        target.setType(source.getType());
        target.setWebSiteUrl(source.getWebSiteUrl());
        target.setFileName(source.getFileName());
        target.setWebsiteTaskStatus(source.getWebsiteTaskStatus());
        target.setWebsiteTaskId(source.getWebsiteTaskId());
        target.setWebsiteTaskError(source.getWebsiteTaskError());
        target.setCollectionName(source.getCollectionName());
        target.setDeleted(source.getDeleted());
        target.setDocType(source.getDocType());
        target.setDocId(source.getDocId());
        target.setFilter(source.getFilter());
        target.setDocMetaData(source.getDocMetaData());
        target.setIsContext(source.getIsContext());
        target.setTables(source.getTables());
        target.setIntegration(source.getIntegration());
        target.setReturnDirect(source.getReturnDirect());
        log.debug("Successfully mapped properties from source to target knowledge base");
    }

    /**
     * Checks if a knowledge base with the given name exists.
     *
     * @param name The name of the knowledge base to check
     * @return A map containing the existence status ("exists" boolean)
     */
    @Override
    public Map<String, Boolean> existsKnowledgeBaseName(String name) {
        log.debug("Checking existence of knowledge base with name: {}", name);
        boolean exists = knowledgeDao.findByName(name) != null;
        log.info("Knowledge base '{}' existence check result: {}", name, exists);
        return Map.of(PromptConstants.RESULT, exists);
    }


}