package com.enttribe.promptanalyzer.dao;

import com.enttribe.promptanalyzer.model.TestCase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Data Access Object interface for TestCase entity operations.
 * Provides methods to interact with the TestCase table in the database.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository
public interface TestCaseDao extends JpaRepository<TestCase, Integer> {

    /**
     * Finds a test case by its testcaseId.
     *
     * @param testcaseId The testcaseId to search for
     * @return Optional containing the test case if found, empty otherwise
     */
    Optional<TestCase> findByTestcaseId(String testcaseId);
}
