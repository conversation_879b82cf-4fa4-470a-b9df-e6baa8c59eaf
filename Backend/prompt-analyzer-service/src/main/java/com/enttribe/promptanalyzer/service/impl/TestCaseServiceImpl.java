package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dao.TestCaseDao;
import com.enttribe.promptanalyzer.dto.TestCaseRequestDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.model.TestCase;
import com.enttribe.promptanalyzer.service.TestCaseService;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import com.enttribe.promptanalyzer.util.CSVUtils;
import com.enttribe.promptanalyzer.util.IdUtil;
import com.enttribe.promptanalyzer.util.TestCaseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVRecord;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * Implementation of the {@link TestCaseService} interface.
 * This class provides comprehensive management of test cases including creation, updates,
 * deletion, and search operations. It handles the persistence and retrieval of test cases,
 * supporting both basic CRUD operations and advanced search capabilities with filtering
 * and pagination.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TestCaseServiceImpl implements TestCaseService {

    private final TestCaseDao testCaseDao;
    private final CustomFilter customFilter;
    private final PromptDao promptDao;

    private static final List<String> csvColumnHeader = List.of("TestcaseId", "InputJson", "Assertions", "Remark", "PromptId");
    private static final int MAX_IMPORT_RECORDS = 1000;

    /**
     * Creates a new test case in the system.
     *
     * @param requestDto The DTO containing test case information
     * @return A map containing the result of the operation
     */
    @Override
    public Map<String, String> create(TestCaseRequestDto requestDto) {
        log.debug("Creating test case");
        try {
            TestCase testCase = TestCaseUtils.getTestCase(requestDto);
            testCase.setDeleted(false);
            TestCase saved = testCaseDao.save(testCase);
            saved.setTestcaseId(IdUtil.getTestCaseId(saved.getId()));

            testCase.setCreatedTime(new Date());
            testCaseDao.save(testCase);
            log.info("Test case created successfully with ID: {}", saved.getId());
            return Map.of(PromptConstants.RESULT, PromptConstants.SUCCESS);
        } catch (Exception e) {
            log.error("Error while creating test case: {}", e.getMessage(), e);
            throw new BusinessException("Unable to create test case", e);
        }
    }


    @Override
    public Map<String, String> createBatch(List<TestCaseRequestDto> requestDtos) {
        if (requestDtos == null || requestDtos.isEmpty()) {
            throw new ResourceNotFoundException("no data is provided");
        }

        try {
            List<TestCase> testCases = requestDtos.stream()
                    .map(TestCaseUtils::getTestCase).toList();

            List<TestCase> savedTestCases = testCaseDao.saveAll(testCases);
            for (TestCase testCase : savedTestCases) {
                testCase.setTestcaseId(IdUtil.getTestCaseId(testCase.getId()));
                testCase.setDeleted(false);
            }
            savedTestCases = testCaseDao.saveAll(testCases);

            boolean isSuccess = savedTestCases.size() == testCases.size(); // basic validation

            return Map.of(
                    PromptConstants.RESULT, isSuccess ? PromptConstants.SUCCESS : PromptConstants.PARTIAL_SUCCESS
            );
        } catch (Exception e) {
            log.error("Error while creating test cases: {}", e.getMessage(), e);
            throw new BusinessException("Unable to create test cases", e);
        }
    }


    /**
     * Updates an existing test case with new information.
     * Only non-null fields in the updated test case will be modified.
     *
     * @param updatedTestcase The test case containing updated information
     * @return A map containing the result of the operation
     * @throws BusinessException if the test case is not found or update fails
     */
    @Override
    public Map<String, String> update(TestCase updatedTestcase) {
        log.debug("Updating test case with ID: {}", updatedTestcase.getId());
        try {
            Integer id = updatedTestcase.getId();
            Optional<TestCase> existingTestcaseOptional = testCaseDao.findById(id);

            if (existingTestcaseOptional.isEmpty()) {
                log.warn("Test case with ID {} not found for update", id);
                throw new BusinessException("Test case not found");
            }

            TestCase existingTestcase = existingTestcaseOptional.get();
            if (updatedTestcase.getInputJson() != null) {
                existingTestcase.setInputJson(updatedTestcase.getInputJson());
            }
            if (updatedTestcase.getRemark() != null) {
                existingTestcase.setRemark(updatedTestcase.getRemark());
            }
            if (updatedTestcase.getPrompt() != null) {
                existingTestcase.setPrompt(updatedTestcase.getPrompt());
            }
            if (updatedTestcase.getAssertions() != null) {
                existingTestcase.setAssertions(updatedTestcase.getAssertions());
            }

            existingTestcase.setModifiedTime(new Date());
            testCaseDao.save(existingTestcase);
            log.info("Test case with ID {} updated successfully", id);

            return Map.of("result", "success");
        } catch (Exception e) {
            log.error("Error while updating test case: {}", e.getMessage(), e);
            throw new BusinessException("Unable to update test case", e);
        }
    }

    /**
     * Performs a soft delete of a test case by marking it as deleted.
     *
     * @param id The ID of the test case to delete
     * @return A map containing the result of the operation
     * @throws BusinessException if the test case is not found or deletion fails
     */
    @Override
    public Map<String, String> deleteTestcase(Integer id) {
        log.debug("Attempting to delete test case with ID: {}", id);
        try {
            Optional<TestCase> testcaseOpt = testCaseDao.findById(id);

            if (testcaseOpt.isEmpty()) {
                log.warn("Test case with ID {} not found for deletion", id);
                throw new BusinessException("Test case not found");
            }

            TestCase testcase = testcaseOpt.get();
            testcase.setDeleted(true);
            testcase.setModifiedTime(new Date());
            testCaseDao.save(testcase);
            log.info("Test case with ID {} marked as deleted", id);

            return Map.of("result", "success");
        } catch (Exception e) {
            log.error("Error while deleting test case: {}", e.getMessage(), e);
            throw new BusinessException("Unable to delete test case", e);
        }
    }

    /**
     * Searches for test cases based on the provided filter criteria.
     *
     * @param filter The filter criteria to apply
     * @param offset The number of records to skip
     * @param size The maximum number of records to return
     * @param orderBy The field to order the results by
     * @param orderType The type of ordering (ascending/descending)
     * @return A list of test cases matching the search criteria
     */
    @Override
    public List<TestCase> search(String filter, Integer offset, Integer size, String orderBy, String orderType) {
        return customFilter.searchByFilter(TestCase.class, filter, orderBy, orderType, offset, size);
    }

    /**
     * Counts the number of test cases matching the provided filter criteria.
     *
     * @param filter The filter criteria to apply
     * @return The number of matching test cases
     */
    @Override
    public Long count(String filter) {
        return customFilter.countByFilter(TestCase.class, filter);
    }

    @Override
    public ResponseEntity<Resource> importTestCase(MultipartFile file) {
        log.debug("Inside importTestCase method");

        try {
            Function<CSVRecord, TestCase> recordTransformer = csvRecord -> {
                TestCase testCase = new TestCase();
                testCase.setTestcaseId(csvRecord.get("TestcaseId"));
                testCase.setInputJson(csvRecord.get("InputJson"));
                testCase.setAssertions(csvRecord.get("Assertions"));
                testCase.setRemark(csvRecord.get("Remark"));

                String promptId = csvRecord.get("PromptId");
                if (promptId != null && !promptId.isEmpty()) {
                    Prompt prompt = promptDao.findByPromptId(promptId);
                    if (prompt == null) {
                        throw new ResourceNotFoundException("Prompt not found with ID: " + promptId);
                    }
                    testCase.setPrompt(prompt);
                }

                return testCase;
            };

            String exportedFileName = file.getOriginalFilename().replaceAll("\\.[^.]*$", "");
            log.info("Importing test cases from file: {}", exportedFileName);
            return CSVUtils.importCSV(file, csvColumnHeader, recordTransformer, this::saveOrUpdateTestCase, exportedFileName, MAX_IMPORT_RECORDS);
        } catch (Exception e) {
            log.error("Error during test case import: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ByteArrayResource(e.getMessage().getBytes()));
        }
    }

    private void saveOrUpdateTestCase(TestCase testCase) {
        log.debug("Saving or updating test case with TestCase ID: {}", testCase.getTestcaseId());
        Optional<TestCase> existingTestCaseOpt = testCaseDao.findByTestcaseId(testCase.getTestcaseId());

        if (existingTestCaseOpt.isPresent()) {
            TestCase existingTestCase = existingTestCaseOpt.get();
            existingTestCase.setInputJson(testCase.getInputJson());
            existingTestCase.setAssertions(testCase.getAssertions());
            existingTestCase.setRemark(testCase.getRemark());
            existingTestCase.setPrompt(testCase.getPrompt());
            existingTestCase.setModifiedTime(new Date());
            testCaseDao.save(existingTestCase);
            log.info("Updating existing test case with ID: {}", testCase.getTestcaseId());
        } else {
            testCase.setDeleted(false);
            testCase.setModifiedTime(new Date());
            testCase.setCreatedTime(new Date());
            log.info("Creating new test case with ID: {}", testCase.getTestcaseId());
            testCaseDao.save(testCase);
        }
        log.info("Test case saved or updated successfully with ID: {}", testCase.getTestcaseId());
    }

    @Override
    public ResponseEntity<Resource> exportTestCasesByIds(List<Integer> testCaseIds) {
        log.debug("Inside method exportTestCase");
        List<TestCase> testCaseList = testCaseDao.findAllById(testCaseIds);
        testCaseList.removeIf(testCase -> !Boolean.FALSE.equals(testCase.getDeleted()));
        return getResponse("testcases",testCaseList);
    }

    private static ResponseEntity<Resource> getResponse(String fileName, List<TestCase> testCaseList) {
        log.debug("Inside method getResponse");
        List<Function<TestCase, Object>> fieldExtractors = getTestCaseFieldExtractors();
        log.debug("Going to export test cases as CSV");
        return CSVUtils.exportCSV(testCaseList, csvColumnHeader, fileName, fieldExtractors);
    }

    private static List<Function<TestCase, Object>> getTestCaseFieldExtractors() {
        return List.of(
            TestCase::getTestcaseId,
            TestCase::getInputJson,
            TestCase::getAssertions,
            TestCase::getRemark,
            testCase -> testCase.getPrompt() != null ? testCase.getPrompt().getPromptId() : "NULL"
        );
    }
}