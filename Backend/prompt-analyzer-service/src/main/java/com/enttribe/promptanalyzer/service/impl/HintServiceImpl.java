package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.config.IntentHintConfig;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.service.HintService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class HintServiceImpl implements HintService {

    private final VectorStore vectorStore;
    private final IntentHintConfig hintConfig;

    public HintServiceImpl(@Qualifier("vectorStoreHint") VectorStore vectorStore,
                           IntentHintConfig hintConfig) {
        this.vectorStore = vectorStore;
        this.hintConfig = hintConfig;
    }

    @Override
    public boolean saveHint(Map<String, Object> requestMap) {
        List<String> hintKeys = (List<String>) requestMap.get("hintKeys");
        String hintValue = (String) requestMap.get(hintConfig.getHintValue());

        try {
            List<Document> hintDocuments = new ArrayList<>();
            for (String hint : hintKeys) {
                log.debug("adding hint: {}", hint);
                Document hintDocument = new Document(hint);
                hintDocument.getMetadata().put("filter", hintConfig.getHintFilter());
                hintDocument.getMetadata().put(hintValue, hintValue);
                hintDocuments.add(hintDocument);
            }
            vectorStore.accept(hintDocuments);
            log.debug("hints saved successfully");
            return true;
        } catch (Exception e) {
            log.error("error while saving hint: {}", e.getMessage(), e);
            throw new BusinessException("unable to save hint", e);
        }
    }

    @Override
    public String searchPlan(Map<String, String> map) {
        String query = map.get("query");
        log.debug("query for search is {}", query);
        SearchRequest searchRequest = SearchRequest.builder()
                .similarityThreshold(hintConfig.getDefaultSimilarityThreshold())
                .filterExpression(String.format(hintConfig.getFilterExpressionFormat(), hintConfig.getHintFilter()))
                .topK(hintConfig.getDefaultTopK())
                .query(query)
                .build();

        List<Document> documents = vectorStore.similaritySearch(searchRequest);
        if (documents.isEmpty()) return "No hints found";

        return (String) documents.getFirst().getMetadata().get(hintConfig.getHintValue());
    }

    @Override
    public List<Map<String, Object>> searchPlanBatch(List<String> queries) {
        log.debug("Processing batch search with {} queries", queries.size());
        List<Map<String, Object>> results = new ArrayList<>();

        for (String queryText : queries) {
            log.debug(PromptConstants.PROCESSING_QUERY_LOG, queryText);

            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(hintConfig.getDefaultSimilarityThreshold())
                    .filterExpression(String.format(hintConfig.getFilterExpressionFormat(), hintConfig.getHintFilter()))
                    .topK(hintConfig.getDefaultTopK())
                    .query(queryText)
                    .build();

            List<Document> documents = vectorStore.similaritySearch(searchRequest);

            Map<String, Object> resultItem = new java.util.HashMap<>();
            resultItem.put("name", queryText);

            if (!documents.isEmpty()) {
                List<Map<String, Object>> metadata = new ArrayList<>();
                for (Document doc : documents) {
                    metadata.add(doc.getMetadata());
                }
                resultItem.put(hintConfig.getMetadataKey(), metadata);
            } else {
                resultItem.put(hintConfig.getMetadataKey(), List.of());
            }

            results.add(resultItem);
        }
        return results;
    }

    @Override
    public List<Map<String, Object>> searchPlanBatchV1(List<String> queries, String type) {
        log.debug("Processing batch search with {} queries and type {}", queries.size(), type);
        List<Map<String, Object>> results = new ArrayList<>();

        String hintFilter = type.equals("who") ? hintConfig.getHintFilter() : "What";
        for (String queryText : queries) {
            log.debug(PromptConstants.PROCESSING_QUERY_LOG, queryText);

            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(hintConfig.getExactMatchScoreThreshold())
                    .filterExpression(String.format(hintConfig.getFilterExpressionFormat(), hintFilter))
                    .topK(hintConfig.getDefaultTopK())
                    .query(queryText)
                    .build();

            List<Document> documents = vectorStore.similaritySearch(searchRequest);

            if (documents.isEmpty() && queryText != null && !queryText.isEmpty()) {
                String[] nameParts = queryText.split("\\s+");
                String firstNameQuery = nameParts[0];

                SearchRequest partialRequest = SearchRequest.builder()
                        .similarityThreshold(hintConfig.getDefaultSimilarityThreshold())
                        .filterExpression(String.format(hintConfig.getFilterExpressionFormat(), hintFilter))
                        .topK(hintConfig.getDefaultTopK())
                        .query(firstNameQuery)
                        .build();

                documents = vectorStore.similaritySearch(partialRequest);
            }

            Map<String, Object> resultItem = new java.util.HashMap<>();
            resultItem.put("name", queryText);

            if (!documents.isEmpty()) {
                List<Map<String, Object>> metadata = new ArrayList<>();
                for (Document doc : documents) {
                    metadata.add(doc.getMetadata());
                }
                resultItem.put(hintConfig.getMetadataKey(), metadata);
            } else {
                resultItem.put(hintConfig.getMetadataKey(), List.of());
            }

            results.add(resultItem);
        }
        return results;
    }

    @Override
    public List<Map<String, Object>> searchPlanBatch(List<String> queries, String type, String entityType) {
        log.debug("Processing batch search with {} queries, type {}, entityType : {}", queries.size(), type, entityType);
        List<Map<String, Object>> results = new ArrayList<>();

        String hintFilter = type.equals("who") ? hintConfig.getHintFilter() : "What";
        for (String queryText : queries) {
            log.debug(PromptConstants.PROCESSING_QUERY_LOG, queryText);

            String filterExpressionFormat = hintConfig.getFilterExpressionFormat();//filter == '%s'
            if (entityType != null) {
                log.debug("entityType is : {}", entityType);
                String entityFilter = String.format(" && entity == '%s'", entityType);
                filterExpressionFormat = filterExpressionFormat + entityFilter;
            }

            SearchRequest searchRequest = SearchRequest.builder()
                    .similarityThreshold(hintConfig.getExactMatchScoreThreshold())
                    .filterExpression(String.format(filterExpressionFormat, hintFilter))
                    .topK(hintConfig.getDefaultTopK())
                    .query(queryText)
                    .build();

            List<Document> documents = vectorStore.similaritySearch(searchRequest);

            if (documents.isEmpty() && queryText != null && !queryText.isEmpty()) {
                String[] nameParts = queryText.split("\\s+");
                String firstNameQuery = nameParts[0];

                SearchRequest partialRequest = SearchRequest.builder()
                        .similarityThreshold(hintConfig.getDefaultSimilarityThreshold())
                        .filterExpression(String.format(filterExpressionFormat, hintFilter))
                        .topK(hintConfig.getDefaultTopK())
                        .query(firstNameQuery)
                        .build();

                documents = vectorStore.similaritySearch(partialRequest);
            }

            Map<String, Object> resultItem = new java.util.HashMap<>();
            resultItem.put("name", queryText);

            if (!documents.isEmpty()) {
                List<Map<String, Object>> metadata = new ArrayList<>();
                for (Document doc : documents) {
                    metadata.add(doc.getMetadata());
                }
                resultItem.put(hintConfig.getMetadataKey(), metadata);
            } else {
                resultItem.put(hintConfig.getMetadataKey(), List.of());
            }

            results.add(resultItem);
        }
        return results;
    }

}
