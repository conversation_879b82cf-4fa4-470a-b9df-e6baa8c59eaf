package com.enttribe.promptanalyzer.config;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.core5.util.Timeout;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.util.TimeValue;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * A singleton class that provides a shared {@link RestTemplate} instance across the application.
 * This class ensures that only one instance of {@link RestTemplate} is created and reused,
 * following the singleton design pattern.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class RestTemplateSingleton {

    private static final int CONNECT_TIMEOUT_MS = 10000;
    private static final int READ_TIMEOUT_MS = 10000;
    private static final int CONNECTION_REQUEST_TIMEOUT_MS = 10000;

    private static RestTemplate restTemplate;

    private RestTemplateSingleton() {
    }

    public static RestTemplate getRestTemplate() {
        if (restTemplate == null) {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(Timeout.ofMilliseconds(CONNECT_TIMEOUT_MS)) // or setConnectTimeout alternative
                    .setResponseTimeout(Timeout.ofMilliseconds(READ_TIMEOUT_MS))
                    .setConnectionRequestTimeout(Timeout.ofMilliseconds(CONNECTION_REQUEST_TIMEOUT_MS))
                    .build();

            PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
            connManager.setDefaultSocketConfig(SocketConfig.custom()
                    .setSoTimeout(Timeout.ofMilliseconds(READ_TIMEOUT_MS))
                    .build());
            connManager.setMaxTotal(PromptConstants.MAX_TOTAL_CONNECTIONS);
            connManager.setDefaultMaxPerRoute(PromptConstants.MAX_CONNECTIONS_PER_ROUTE);

            HttpClient httpClient = HttpClients.custom()
                    .setConnectionManager(connManager)
                    .setDefaultRequestConfig(requestConfig)
                    .evictIdleConnections(TimeValue.ofSeconds(PromptConstants.IDLE_CONNECTION_EVICTION_TIME_SECONDS))
                    .build();

            // IMPORTANT: must use Apache 5-compatible factory
            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setHttpClient(httpClient);

            restTemplate = new RestTemplate(factory);

        }
        return restTemplate;
    }
}
