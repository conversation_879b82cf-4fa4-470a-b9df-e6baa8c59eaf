package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.dao.McpServerDao;
import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.sql.CustomFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class McpServerServiceImplTest {

    @Mock
    private McpServerDao mcpServerDao;

    @Mock
    private CustomFilter customFilter;

    @InjectMocks
    private McpServerServiceImpl mcpServerService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateSuccess() {
        McpServerDto dto = new McpServerDto();
        dto.setName("Test Server");

        when(mcpServerDao.save(any(McpServer.class))).thenReturn(new McpServer());

        Map<String, String> result = mcpServerService.create(dto);

        assertEquals("success", result.get("result"));
        verify(mcpServerDao, times(1)).save(any(McpServer.class));
    }

    @Test
    void testCreateThrowsException() {
        McpServerDto dto = new McpServerDto();
        dto.setName("Failing Server");

        doThrow(new RuntimeException("DB error")).when(mcpServerDao).save(any());

        BusinessException ex = assertThrows(BusinessException.class, () -> mcpServerService.create(dto));
        assertTrue(ex.getMessage().contains("Unable to save MCP Server"));
    }

    @Test
    void testUpdateSuccess() {
        McpServerDto dto = new McpServerDto();
        dto.setId(1);
        dto.setName("Updated Server");

        McpServer server = new McpServer();
        server.setId(1);

        when(mcpServerDao.findById(1)).thenReturn(Optional.of(server));
        when(mcpServerDao.save(any())).thenReturn(server);

        Map<String, String> result = mcpServerService.update(dto);
        assertEquals("success", result.get("result"));
        verify(mcpServerDao).save(any(McpServer.class));
    }

    @Test
    void testUpdateWithNullId() {
        McpServerDto dto = new McpServerDto(); // no ID set

        BusinessException ex = assertThrows(BusinessException.class, () -> mcpServerService.update(dto));
        assertEquals("Unable to update MCP Server: Server ID cannot be null for update operation", ex.getMessage());
    }


    @Test
    void testUpdateNotFound() {
        McpServerDto dto = new McpServerDto();
        dto.setId(1);

        when(mcpServerDao.findById(1)).thenReturn(Optional.empty());

        BusinessException ex = assertThrows(BusinessException.class, () -> mcpServerService.update(dto));
        assertEquals("Unable to update MCP Server: MCP Server not found with ID: 1", ex.getMessage());
    }


    @Test
    void testSearchSuccess() {
        when(customFilter.searchByFilter(any(), any(), any(), any(), any(), any())).thenReturn(List.of(new McpServer()));

        List<McpServer> result = mcpServerService.search("name:Test", 0, 10, "name", "asc");

        assertEquals(1, result.size());
        verify(customFilter).searchByFilter(any(), eq("name:Test"), eq("name"), eq("asc"), eq(0), eq(10));
    }

    @Test
    void testCountSuccess() {
        when(customFilter.countByFilter(any(), any())).thenReturn(5L);
        Long count = mcpServerService.count("active:true");

        assertEquals(5L, count);
    }

    @Test
    void testSoftDeleteSuccess() {
        McpServer server = new McpServer();
        server.setId(1);

        when(mcpServerDao.findById(1)).thenReturn(Optional.of(server));
        when(mcpServerDao.save(any())).thenReturn(server);

        Map<String, String> result = mcpServerService.softDelete(1);

        assertEquals("success", result.get("result"));
        verify(mcpServerDao).save(any(McpServer.class));
    }

    @Test
    void testSoftDeleteNotFound() {
        when(mcpServerDao.findById(999)).thenReturn(Optional.empty());

        Map<String, String> result = mcpServerService.softDelete(999);

        assertEquals("failed", result.get("result"));
        assertTrue(result.get("message").contains("MCP Server not found with ID:"));
    }
}
