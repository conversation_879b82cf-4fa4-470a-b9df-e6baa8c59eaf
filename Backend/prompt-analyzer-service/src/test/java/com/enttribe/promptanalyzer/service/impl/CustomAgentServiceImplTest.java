package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.audit.AuditAdvisor;
import com.enttribe.promptanalyzer.dao.AgentHistoryDao;
import com.enttribe.promptanalyzer.dto.agent.CustomAgentDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.AgentHistory;
import com.enttribe.promptanalyzer.model.AgentMessage;
import com.enttribe.promptanalyzer.service.ApiService;
import com.enttribe.promptanalyzer.service.NifiFlowService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.core.io.Resource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CustomAgentServiceImpl.
 * Tests all major functionality including plan generation, conversation handling,
 * and trigger name creation.
 *
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
class CustomAgentServiceImplTest {

    @Mock
    private AgentHistoryDao agentHistoryDao;

    @Mock
    private NifiFlowService nifiFlowService;

    @Mock
    private VectorStore vectorStore;

    @Mock
    private InferenceManager inferenceManager;

    @Mock
    private AuditAdvisor auditAdvisor;

    @Mock
    private ApiService apiService;

    @Mock
    private Resource orchestratorPrompt;

    @Mock
    private Resource populateProcessorsPrompt;

    @Mock
    private Resource identifyProcessorsPrompt;

    @Mock
    private Resource improveQueryPrompt;

    @Mock
    private ChatModel chatModel;

    @Mock
    private ChatClient chatClient;

    @Mock
    private ChatClient.ChatClientRequestSpec requestSpec;

    @Mock
    private ChatClient.CallResponseSpec callResponseSpec;

    @InjectMocks
    private CustomAgentServiceImpl customAgentService;

    private CustomAgentDto testAgentDto;
    private AgentHistory testAgentHistory;
    private Map<String, String> testProcessorMap;

    @BeforeEach
    void setUp() {
        // Initialize test data
        testAgentDto = new CustomAgentDto();
        testAgentDto.setUserQuery("Create a flow to process customer data");
        testAgentDto.setProcessGroupId("test-process-group");
        testAgentDto.setTimeStamp(new Date());

        testAgentHistory = new AgentHistory();
        testAgentHistory.setAgentName("test-agent");
        testAgentHistory.setAgentMessages(new ArrayList<>());

        testProcessorMap = Map.of(
                "processor1", "Test Processor 1",
                "processor2", "Test Processor 2"
        );

        // Set up processor map using reflection
        ReflectionTestUtils.setField(customAgentService, "processorMap", testProcessorMap);
        ReflectionTestUtils.setField(customAgentService, "orchestratorPrompt", orchestratorPrompt);
        ReflectionTestUtils.setField(customAgentService, "populateProcessorsPrompt", populateProcessorsPrompt);
        ReflectionTestUtils.setField(customAgentService, "identifyProcessorsPrompt", identifyProcessorsPrompt);
        ReflectionTestUtils.setField(customAgentService, "improveQueryPrompt", improveQueryPrompt);
    }

    @Test
    @DisplayName("Should initialize processor map successfully")
    void testInit() {
        // Arrange
        Map<String, String> expectedProcessorMap = Map.of("proc1", "Processor 1");
        when(apiService.getProcessors()).thenReturn(expectedProcessorMap);

        // Act
        customAgentService.init();

        // Assert
        verify(apiService).getProcessors();
        Map<String, String> actualProcessorMap = (Map<String, String>) ReflectionTestUtils.getField(customAgentService, "processorMap");
        assertEquals(expectedProcessorMap, actualProcessorMap);
    }

    @Test
    @DisplayName("Should generate plan for user query successfully - new conversation")
    void testGetPlanforUserQuery_NewConversation_Success() {
        // Arrange
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        // Mock ChatClient creation and responses
        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorIdentification();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertNotNull(result.get("conversationId"));
        assertNotNull(result.get("message"));
        verify(agentHistoryDao).save(any(AgentHistory.class));
    }

    @Test
    @DisplayName("Should generate plan for existing conversation")
    void testGetPlanforUserQuery_ExistingConversation_Success() {
        // Arrange
        testAgentDto.setConversationId("existing-conversation-id");
        AgentMessage existingMessage = new AgentMessage();
        existingMessage.setUserMessage("Previous message");
        existingMessage.setAssistantMessage("{\"message\":\"Previous response\",\"plan\":\"[]\"}");
        testAgentHistory.getAgentMessages().add(existingMessage);

        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorIdentification();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertEquals("existing-conversation-id", result.get("conversationId"));
        verify(agentHistoryDao).save(any(AgentHistory.class));
    }

    @Test
    @DisplayName("Should handle validation flow acceptance")
    void testGetPlanforUserQuery_ValidationFlow_Success() {
        // Arrange
        testAgentDto.setUserQuery("I accept the flow object");
        testAgentDto.setConversationId("test-conversation-id");
        
        List<String> validationErrors = List.of("Error 1", "Error 2");
        when(nifiFlowService.getValidationErrorsProcessGroup(anyString())).thenReturn(validationErrors);
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertEquals("", result.get("plan")); // Plan should be empty for validation flow
        verify(nifiFlowService).getValidationErrorsProcessGroup(anyString());
    }

    @Test
    @DisplayName("Should handle exception during plan generation")
    void testGetPlanforUserQuery_Exception() {
        // Arrange
        when(agentHistoryDao.getAgentHistory(anyString())).thenThrow(new RuntimeException("Database error"));

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertNotNull(result.get("conversationId"));
        assertTrue(result.get("message").contains("error") || result.get("message").contains("issue"));
    }

    @Test
    @DisplayName("Should create chat client with correct configuration")
    void testCreateChatClient() {
        // Arrange
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        // Act
        ChatClient result = customAgentService.createChatClient();

        // Assert
        assertNotNull(result);
        verify(inferenceManager).getChatModelByProvider("groq");
    }

    @Test
    @DisplayName("Should create trigger name and description successfully")
    void testCreateTriggerNameDescription_Success() {
        // Arrange
        String userQuery = "Process customer payment data from Kafka to PostgreSQL";
        String expectedResponse = "{\"name\":\"Customer Payment Processing\",\"description\":\"Process customer payment data from Kafka and store in PostgreSQL\"}";
        
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);
        mockChatClientForTriggerName(expectedResponse);

        // Act
        Map<String, String> result = customAgentService.createTriggerNameDescription(userQuery);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertEquals(expectedResponse, result.get("data"));
    }

    @Test
    @DisplayName("Should handle exception during trigger name creation")
    void testCreateTriggerNameDescription_Exception() {
        // Arrange
        String userQuery = "Process customer data";
        when(inferenceManager.getChatModelByProvider("groq")).thenThrow(new RuntimeException("LLM error"));

        // Act
        Map<String, String> result = customAgentService.createTriggerNameDescription(userQuery);

        // Assert
        assertNotNull(result);
        assertEquals("failed", result.get("status"));
    }

    // Helper methods for mocking complex interactions
    private void mockChatClientCreation() {
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);
    }

    private void mockOrchestratorResponse() {
        // Mock orchestrator response
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenReturn("{\"changeProcessor\":true,\"fillProperties\":false,\"reasoning\":\"Adding new processor\"}");
    }

    private void mockProcessorIdentification() {
        // Mock processor identification response
        when(callResponseSpec.content()).thenReturn(
            "{\"matchedProcessors\":[{\"name\":\"Test Processor\",\"id\":\"processor1\"}],\"isAskingForProcessorsList\":false,\"reasoning\":\"Found matching processor\"}"
        );
    }

    private void mockProcessorPopulation() {
        // Mock processor population response
        when(callResponseSpec.content()).thenReturn(
            "{\"processors\":[{\"name\":\"TestProcessor\",\"package\":\"test.package\",\"description\":\"Test processor\",\"type\":\"ACTION\",\"condition\":{}}],\"message\":\"Processor configured successfully\"}"
        );
    }

    private void mockChatClientForTriggerName(String response) {
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);

        ChatClient.ChatClientResponse chatResponse = mock(ChatClient.ChatClientResponse.class);
        ChatClient.ChatClientResponse.ChatResult chatResult = mock(ChatClient.ChatClientResponse.ChatResult.class);
        ChatClient.ChatClientResponse.ChatResult.Output output = mock(ChatClient.ChatClientResponse.ChatResult.Output.class);

        when(callResponseSpec.chatResponse()).thenReturn(chatResponse);
        when(chatResponse.getResult()).thenReturn(chatResult);
        when(chatResult.getOutput()).thenReturn(output);
        when(output.getText()).thenReturn(response);
    }

    @Test
    @DisplayName("Should handle empty processor identification")
    void testGetPlanforUserQuery_EmptyProcessors() {
        // Arrange
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();

        // Mock empty processor identification
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenReturn(
            "{\"matchedProcessors\":[],\"isAskingForProcessorsList\":false,\"reasoning\":\"No matching processors found\"}"
        );

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertTrue(result.get("message").contains("couldn't find a processor"));
    }

    @Test
    @DisplayName("Should handle user asking for processor list")
    void testGetPlanforUserQuery_AskingForProcessorsList() {
        // Arrange
        testAgentDto.setUserQuery("What processors are available?");
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();

        // Mock asking for processors list
        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenReturn(
            "{\"matchedProcessors\":[],\"isAskingForProcessorsList\":true,\"reasoning\":\"User wants processor list\"}"
        );

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertTrue(result.get("message").contains("can't provide processors list"));
    }

    @Test
    @DisplayName("Should handle null agent history")
    void testGetPlanforUserQuery_NullAgentHistory() {
        // Arrange
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.empty());
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(new AgentHistory());
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorIdentification();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        verify(agentHistoryDao).save(any(AgentHistory.class));
    }

    @Test
    @DisplayName("Should test orchestrator response with fill properties true")
    void testGetOrchestratorResponse_FillPropertiesTrue() {
        // Arrange
        String userQuery = "Update processor properties";
        String conversationHistory = "Previous conversation";

        when(chatClient.prompt()).thenReturn(requestSpec);
        when(requestSpec.system(anyString())).thenReturn(requestSpec);
        when(requestSpec.user(anyString())).thenReturn(requestSpec);
        when(requestSpec.call()).thenReturn(callResponseSpec);
        when(callResponseSpec.content()).thenReturn(
            "{\"changeProcessor\":false,\"fillProperties\":true,\"reasoning\":\"Updating properties only\"}"
        );

        // Act
        CustomAgentServiceImpl.OrchestratorResponse result = customAgentService.getOrchestratorResponse(userQuery, chatClient, conversationHistory);

        // Assert
        assertNotNull(result);
        assertFalse(result.changeProcessor());
        assertTrue(result.fillProperties());
        assertEquals("Updating properties only", result.reasoning());
    }

    @Test
    @DisplayName("Should handle orchestrator response exception")
    void testGetOrchestratorResponse_Exception() {
        // Arrange
        String userQuery = "Test query";
        String conversationHistory = "Test history";

        when(chatClient.prompt()).thenThrow(new RuntimeException("LLM error"));

        // Act
        CustomAgentServiceImpl.OrchestratorResponse result = customAgentService.getOrchestratorResponse(userQuery, chatClient, conversationHistory);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should test processor population with empty processors")
    void testPopulateProcessors_EmptyProcessors() {
        // Arrange
        String userQuery = "Test query";
        String identifiedProcessors = "[]";
        String currentPlan = "";

        // Act
        CustomAgentServiceImpl.PopulatedProcessors result = customAgentService.populateProcessors(userQuery, chatClient, identifiedProcessors, currentPlan);

        // Assert
        assertNotNull(result);
        assertTrue(result.processors().isEmpty());
        assertTrue(result.message().contains("couldn't find a processor"));
    }

    @Test
    @DisplayName("Should test processor population exception")
    void testPopulateProcessors_Exception() {
        // Arrange
        String userQuery = "Test query";
        String identifiedProcessors = "[\"processor1\"]";
        String currentPlan = "";

        when(chatClient.prompt()).thenThrow(new RuntimeException("LLM error"));

        // Act
        CustomAgentServiceImpl.PopulatedProcessors result = customAgentService.populateProcessors(userQuery, chatClient, identifiedProcessors, currentPlan);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should test processor identification exception")
    void testIdentifyProcessors_Exception() {
        // Arrange
        String userQuery = "Test query";
        String conversationId = "test-id";
        String conversationHistory = "Test history";

        when(chatClient.prompt()).thenThrow(new RuntimeException("LLM error"));

        // Act
        CustomAgentServiceImpl.ProcessorMatchResponse result = customAgentService.identifyProcessors(userQuery, chatClient, conversationId, conversationHistory);

        // Assert
        assertNull(result);
    }

    @Test
    @DisplayName("Should validate CustomAgentDto fields")
    void testCustomAgentDto_Validation() {
        // Arrange & Act
        CustomAgentDto dto = new CustomAgentDto();
        dto.setUserQuery("Test query");
        dto.setProcessGroupId("test-group");
        dto.setConversationId("test-conversation");
        dto.setTimeStamp(new Date());

        // Assert
        assertEquals("Test query", dto.getUserQuery());
        assertEquals("test-group", dto.getProcessGroupId());
        assertEquals("test-conversation", dto.getConversationId());
        assertNotNull(dto.getTimeStamp());
    }

    @Test
    @DisplayName("Should test plan merging functionality")
    void testMergePlans_Success() {
        // This tests the private mergePlans method indirectly through getPlanforUserQuery
        // when validation flow is accepted

        // Arrange
        testAgentDto.setUserQuery("I accept the flow object");
        testAgentDto.setConversationId("test-conversation-id");

        AgentMessage existingMessage = new AgentMessage();
        existingMessage.setUserMessage("Previous message");
        existingMessage.setAssistantMessage("{\"message\":\"Previous response\",\"plan\":\"[{\\\"name\\\":\\\"TestProcessor\\\",\\\"condition\\\":{\\\"prop1\\\":\\\"value1\\\"}}]\"}");
        testAgentHistory.getAgentMessages().add(existingMessage);
        testAgentHistory.setCurrentPlan("[{\"name\":\"TestProcessor\",\"condition\":{\"prop2\":\"value2\"}}]");

        when(nifiFlowService.getValidationErrorsProcessGroup(anyString())).thenReturn(List.of());
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        verify(agentHistoryDao).save(any(AgentHistory.class));
    }

    @Test
    @DisplayName("Should test OrchestratorResponse record")
    void testOrchestratorResponse_Record() {
        // Arrange & Act
        CustomAgentServiceImpl.OrchestratorResponse response =
            new CustomAgentServiceImpl.OrchestratorResponse(true, false, "Test reasoning");

        // Assert
        assertTrue(response.changeProcessor());
        assertFalse(response.fillProperties());
        assertEquals("Test reasoning", response.reasoning());
    }

    @Test
    @DisplayName("Should test ProcessorMatchResponse record")
    void testProcessorMatchResponse_Record() {
        // Arrange
        List<CustomAgentServiceImpl.MatchedProcessor> processors = List.of(
            new CustomAgentServiceImpl.MatchedProcessor("Test Processor", "proc1")
        );

        // Act
        CustomAgentServiceImpl.ProcessorMatchResponse response =
            new CustomAgentServiceImpl.ProcessorMatchResponse(processors, false, "Found processors");

        // Assert
        assertEquals(1, response.matchedProcessors().size());
        assertEquals("Test Processor", response.matchedProcessors().get(0).name());
        assertEquals("proc1", response.matchedProcessors().get(0).id());
        assertFalse(response.isAskingForProcessorsList());
        assertEquals("Found processors", response.reasoning());
    }

    @Test
    @DisplayName("Should test MatchedProcessor record")
    void testMatchedProcessor_Record() {
        // Arrange & Act
        CustomAgentServiceImpl.MatchedProcessor processor =
            new CustomAgentServiceImpl.MatchedProcessor("Test Processor", "test-id");

        // Assert
        assertEquals("Test Processor", processor.name());
        assertEquals("test-id", processor.id());
    }

    @Test
    @DisplayName("Should test PopulatedProcessors record")
    void testPopulatedProcessors_Record() {
        // Arrange
        Map<String, String> condition = Map.of("prop1", "value1");
        CustomAgentServiceImpl.Processor processor =
            new CustomAgentServiceImpl.Processor("TestProc", "com.test", "Test description", "ACTION", condition);
        List<CustomAgentServiceImpl.Processor> processors = List.of(processor);

        // Act
        CustomAgentServiceImpl.PopulatedProcessors populatedProcessors =
            new CustomAgentServiceImpl.PopulatedProcessors(processors, "Success message");

        // Assert
        assertEquals(1, populatedProcessors.processors().size());
        assertEquals("Success message", populatedProcessors.message());
    }

    @Test
    @DisplayName("Should test Processor record")
    void testProcessor_Record() {
        // Arrange
        Map<String, String> condition = Map.of("property1", "value1", "property2", "value2");

        // Act
        CustomAgentServiceImpl.Processor processor =
            new CustomAgentServiceImpl.Processor(
                "TestProcessor",
                "com.test.package",
                "Test processor description",
                "TRIGGER",
                condition
            );

        // Assert
        assertEquals("TestProcessor", processor.name());
        assertEquals("com.test.package", processor._package());
        assertEquals("Test processor description", processor.description());
        assertEquals("TRIGGER", processor.type());
        assertEquals(2, processor.condition().size());
        assertEquals("value1", processor.condition().get("property1"));
        assertEquals("value2", processor.condition().get("property2"));
    }

    @Test
    @DisplayName("Should handle conversation context building")
    void testConversationContextBuilding() {
        // This tests the private getConversationContextForOrchestrator method indirectly

        // Arrange
        AgentMessage message1 = new AgentMessage();
        message1.setUserMessage("First user message");
        message1.setAssistantMessage("{\"message\":\"First assistant response\"}");

        AgentMessage message2 = new AgentMessage();
        message2.setUserMessage("Second user message");
        message2.setAssistantMessage("{\"message\":\"Second assistant response\"}");

        testAgentHistory.getAgentMessages().add(message1);
        testAgentHistory.getAgentMessages().add(message2);
        testAgentHistory.setCurrentPlan("[{\"name\":\"TestProcessor\"}]");

        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorIdentification();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        verify(agentHistoryDao).save(any(AgentHistory.class));
    }

    @Test
    @DisplayName("Should handle malformed JSON in agent messages")
    void testMalformedJsonInAgentMessages() {
        // Arrange
        AgentMessage malformedMessage = new AgentMessage();
        malformedMessage.setUserMessage("Test message");
        malformedMessage.setAssistantMessage("Invalid JSON {malformed}");
        testAgentHistory.getAgentMessages().add(malformedMessage);

        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorIdentification();
        mockProcessorPopulation();

        // Act & Assert - Should not throw exception
        assertDoesNotThrow(() -> {
            Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);
            assertNotNull(result);
            assertEquals("success", result.get("status"));
        });
    }

    @Test
    @DisplayName("Should test constants and static values")
    void testConstants() {
        // This test ensures that the service uses the expected constant values
        // We can verify this through the behavior of the methods

        // Arrange
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        // Act
        ChatClient result = customAgentService.createChatClient();

        // Assert
        assertNotNull(result);
        // Verify that the correct provider is used
        verify(inferenceManager).getChatModelByProvider("groq");
    }

    @Test
    @DisplayName("Should handle empty user query")
    void testGetPlanforUserQuery_EmptyUserQuery() {
        // Arrange
        testAgentDto.setUserQuery("");
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));
        when(agentHistoryDao.save(any(AgentHistory.class))).thenReturn(testAgentHistory);
        when(inferenceManager.getChatModelByProvider("groq")).thenReturn(chatModel);

        mockChatClientCreation();
        mockOrchestratorResponse();
        mockProcessorIdentification();
        mockProcessorPopulation();

        // Act
        Map<String, String> result = customAgentService.getPlanforUserQuery(testAgentDto);

        // Assert
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        verify(agentHistoryDao).save(any(AgentHistory.class));
    }

    @Test
    @DisplayName("Should handle null user query")
    void testGetPlanforUserQuery_NullUserQuery() {
        // Arrange
        testAgentDto.setUserQuery(null);
        when(agentHistoryDao.getAgentHistory(anyString())).thenReturn(Optional.of(testAgentHistory));

        // Act & Assert
        assertThrows(Exception.class, () -> {
            customAgentService.getPlanforUserQuery(testAgentDto);
        });
    }
}
