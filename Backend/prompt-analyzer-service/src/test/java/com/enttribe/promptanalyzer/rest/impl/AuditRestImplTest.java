package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.audit.ExceptionAuditDto;
import com.enttribe.promptanalyzer.dto.audit.ToolAuditDto;
import com.enttribe.promptanalyzer.dto.prompt.PromptAuditDto;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.model.PromptAudit;
import com.enttribe.promptanalyzer.model.ToolAudit;
import com.enttribe.promptanalyzer.service.AuditService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

class AuditRestImplTest {

    private MockMvc mockMvc;
    private AuditService auditService;
    private AuditRestImpl auditRest;

    private ExceptionAuditDto exceptionAuditDto;
    private PromptAuditDto promptAuditDto;

    @BeforeEach
    void setUp() {
        auditService = mock(AuditService.class);
        auditRest = new AuditRestImpl(auditService);
        mockMvc = MockMvcBuilders.standaloneSetup(auditRest).build();

        exceptionAuditDto = new ExceptionAuditDto();
        exceptionAuditDto.setAuditId("123");

        promptAuditDto = new PromptAuditDto();
    }

    static String asJsonString(final Object obj) {
        try {
            return new ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            throw new BusinessException("Error converting to JSON", e);
        }
    }

    @Test
    @DisplayName("Save Exception Audit Successfully")
    void saveExceptionAuditSuccess() throws Exception {
        when(auditService.saveExceptionAudit(any())).thenReturn("123");

        mockMvc.perform(post("/audit/exception/save")
                        .content(asJsonString(exceptionAuditDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().string("123"));

        verify(auditService).saveExceptionAudit(any());
    }

    @Test
    @DisplayName("Save Prompt Audit Successfully")
    void savePromptAuditSuccess() throws Exception {
        when(auditService.savePromptAudit(any())).thenReturn("456");

        mockMvc.perform(post("/audit/prompt/save")
                        .content(asJsonString(promptAuditDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(content().string("456"));

        verify(auditService).savePromptAudit(any());
    }

    @Test
    @DisplayName("Get Prompt Audit List by Audit ID Successfully")
    void getPromptAuditListByAuditIdSuccess() throws Exception {
        String auditId = "AUDIT_ID";
        List<PromptAudit> expectedList = Collections.singletonList(new PromptAudit());
        when(auditService.getPromptAuditListByAuditId(auditId)).thenReturn(expectedList);

        mockMvc.perform(get("/audit/getPromptAuditListByAuditId/" + auditId))
                .andExpect(status().isOk())
                .andExpect(content().json(asJsonString(expectedList)));

        verify(auditService).getPromptAuditListByAuditId(auditId);
    }


    @Test
    void testGetPromptAuditListByPromptId() {
        String promptId = "prompt-001";
        List<PromptAudit> expectedList = List.of(new PromptAudit());

        when(auditService.getPromptAuditListByPromptId(promptId)).thenReturn(expectedList);

        List<PromptAudit> result = auditRest.getPromptAuditListByPromptId(promptId);

        assertEquals(expectedList, result);
        verify(auditService).getPromptAuditListByPromptId(promptId);
    }

    @Test
    void testSearchPromptAudit() {
        String filter = "test-filter";
        int offset = 0, size = 10;
        String orderBy = "createdAt";
        String orderType = "desc";
        List<PromptAuditDto> mockList = List.of(new PromptAuditDto());

        when(auditService.searchPromptAudit(filter, offset, size, orderBy, orderType)).thenReturn(mockList);

        List<PromptAuditDto> result = auditRest.searchPromptAudit(filter, offset, size, orderBy, orderType);

        assertEquals(mockList, result);
        verify(auditService).searchPromptAudit(filter, offset, size, orderBy, orderType);
    }

    @Test
    void testCountPromptAudit() {
        String filter = "test-filter";
        Long expectedCount = 5L;

        when(auditService.countPromtAudit(filter)).thenReturn(expectedCount);

        Long result = auditRest.countPromptAudit(filter);

        assertEquals(expectedCount, result);
        verify(auditService).countPromtAudit(filter);
    }

    @Test
    void testSaveToolAudit() {
        ToolAuditDto toolAuditDto = new ToolAuditDto();
        Map<String, String> expectedMap = Map.of("status", "success");

        when(auditService.saveToolAudit(toolAuditDto)).thenReturn(expectedMap);

        Map<String, String> result = auditRest.saveToolAudit(toolAuditDto);

        assertEquals(expectedMap, result);
        verify(auditService).saveToolAudit(toolAuditDto);
    }

    @Test
    void testGetToolAuditListByAuditId() {
        String auditId = "audit-123";
        List<ToolAudit> expectedList = List.of(new ToolAudit());

        when(auditService.getToolAuditListByAuditId(auditId)).thenReturn(expectedList);

        List<ToolAudit> result = auditRest.getToolAuditListByAuditId(auditId);

        assertEquals(expectedList, result);
        verify(auditService).getToolAuditListByAuditId(auditId);
    }


}

