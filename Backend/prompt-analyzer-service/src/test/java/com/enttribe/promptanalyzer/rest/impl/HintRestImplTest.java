package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.service.HintService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class HintRestImplTest {

    @Mock
    private HintService hintService;

    @InjectMocks
    private HintRestImpl hintRest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSaveHints() {
        Map<String, Object> requestMap = Map.of("key", "value");
        when(hintService.saveHint(requestMap)).thenReturn(true);

        Map<String, Boolean> result = hintRest.saveHints(requestMap);

        assertEquals(Map.of("result", true), result);
        verify(hintService).saveHint(requestMap);
    }

    @Test
    void testSearchPlan() {
        Map<String, String> requestMap = Map.of("plan", "basic");
        when(hintService.searchPlan(requestMap)).thenReturn("premium");

        Map<String, String> result = hintRest.searchPlan(requestMap);

        assertEquals(Map.of("result", "premium"), result);
        verify(hintService).searchPlan(requestMap);
    }

    @Test
    void testSearchPlanBatch() {
        List<String> queries = List.of("query1", "query2");
        List<Map<String, Object>> mockResponse = List.of(Map.of("result", "A"), Map.of("result", "B"));

        when(hintService.searchPlanBatch(queries)).thenReturn(mockResponse);

        List<Map<String, Object>> result = hintRest.searchPlanBatch(queries);

        assertEquals(mockResponse, result);
        verify(hintService).searchPlanBatch(queries);
    }

    @Test
    void testSearchPlanBatchV1() {
        List<String> queries = List.of("query1");
        String type = "TYPE_X";
        List<Map<String, Object>> mockResponse = List.of(Map.of("result", "X"));

        when(hintService.searchPlanBatchV1(queries, type)).thenReturn(mockResponse);

        List<Map<String, Object>> result = hintRest.searchPlanBatchV1(queries, type);

        assertEquals(mockResponse, result);
        verify(hintService).searchPlanBatchV1(queries, type);
    }

    @Test
    void testSearchPlanBatchV2() {
        Map<String, Object> request = new HashMap<>();
        List<String> queries = List.of("q1", "q2");
        request.put("queries", queries);
        request.put("type", "TYPE_Y");
        request.put("entity", "ENTITY_Z");

        List<Map<String, Object>> mockResponse = List.of(Map.of("result", "Y"));

        when(hintService.searchPlanBatch(queries, "TYPE_Y", "ENTITY_Z")).thenReturn(mockResponse);

        List<Map<String, Object>> result = hintRest.searchPlanBatchV2(request);

        assertEquals(mockResponse, result);
        verify(hintService).searchPlanBatch(queries, "TYPE_Y", "ENTITY_Z");
    }
}
