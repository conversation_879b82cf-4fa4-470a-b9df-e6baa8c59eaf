package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.dto.tool.ToolDto;
import com.enttribe.promptanalyzer.service.ToolService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class ToolCallbackProviderRestImplTest {

    @Mock
    private ToolService toolService;

    @InjectMocks
    private ToolCallbackProviderRestImpl toolCallbackProviderRest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @InjectMocks
    private ToolRestImpl toolRest;

    @Test
    void testGetToolCallbackProvider() {
        List<HashMap<String, String>> expected = List.of(
                new HashMap<>(Map.of("toolName", "ToolA")),
                new HashMap<>(Map.of("toolName", "ToolB"))
        );

        when(toolService.getToolCallbackProvider()).thenReturn(expected);

        List<HashMap<String, String>> result = toolCallbackProviderRest.getToolCallbackProvider();
        assertEquals(expected, result);
        verify(toolService, times(1)).getToolCallbackProvider();
    }

    @Test
    void testGetToolByName() {
        String toolName = "exampleTool";
        ToolDto expected = new ToolDto();
        when(toolService.getToolByName(toolName)).thenReturn(expected);

        ToolDto actual = toolRest.getToolByName(toolName);

        assertEquals(expected, actual);
        verify(toolService).getToolByName(toolName);
    }

    @Test
    void testGetToolDtoByAgentId() {
        Long agentId = 123L;
        List<ToolDto> expectedList = List.of(new ToolDto(), new ToolDto());
        when(toolService.getToolsDtoByAgentId(agentId)).thenReturn(expectedList);

        List<ToolDto> actualList = toolRest.getToolDtoByAgentId(agentId);

        assertEquals(expectedList, actualList);
        verify(toolService).getToolsDtoByAgentId(agentId);
    }

    @Test
    void testFindToolsByIds() {
        List<Integer> ids = List.of(1, 2, 3);
        List<ToolDto> expectedList = List.of(new ToolDto());
        when(toolService.findToolsByIds(ids)).thenReturn(expectedList);

        List<ToolDto> actualList = toolRest.findToolsByIds(ids);

        assertEquals(expectedList, actualList);
        verify(toolService).findToolsByIds(ids);
    }


}
