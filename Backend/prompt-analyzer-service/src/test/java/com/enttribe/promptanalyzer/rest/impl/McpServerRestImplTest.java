package com.enttribe.promptanalyzer.rest.impl;


import com.enttribe.promptanalyzer.dto.mcpserver.McpServerDto;
import com.enttribe.promptanalyzer.model.McpServer;
import com.enttribe.promptanalyzer.service.McpServerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class McpServerRestImplTest {

    @Mock
    private McpServerService mcpServerService;

    @InjectMocks
    private McpServerRestImpl mcpServerRest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreate() {
        McpServerDto dto = new McpServerDto();
        dto.setName("New Server");

        Map<String, String> expected = Map.of("status", "created");
        when(mcpServerService.create(dto)).thenReturn(expected);

        Map<String, String> result = mcpServerRest.create(dto);
        assertEquals(expected, result);
        verify(mcpServerService).create(dto);
    }

    @Test
    void testUpdate() {
        McpServerDto dto = new McpServerDto();
        dto.setId(1);
        dto.setName("Updated Server");

        Map<String, String> expected = Map.of("status", "updated");
        when(mcpServerService.update(dto)).thenReturn(expected);

        Map<String, String> result = mcpServerRest.update(dto);
        assertEquals(expected, result);
        verify(mcpServerService).update(dto);
    }

    @Test
    void testSearch() {
        List<McpServer> expectedList = List.of(new McpServer());
        when(mcpServerService.search("test", 0, 10, "name", "asc")).thenReturn(expectedList);

        List<McpServer> result = mcpServerRest.search("test", 0, 10, "name", "asc");
        assertEquals(expectedList, result);
        verify(mcpServerService).search("test", 0, 10, "name", "asc");
    }

    @Test
    void testCount() {
        when(mcpServerService.count("filter")).thenReturn(5L);

        Long count = mcpServerRest.count("filter");
        assertEquals(5L, count);
        verify(mcpServerService).count("filter");
    }

    @Test
    void testSoftDelete() {
        Integer id = 1;
        Map<String, String> expected = Map.of("status", "deleted");
        when(mcpServerService.softDelete(id)).thenReturn(expected);

        Map<String, String> result = mcpServerRest.softDelete(id);
        assertEquals(expected, result);
        verify(mcpServerService).softDelete(id);
    }

    @Test
    void testGetMcpServerByIds() {
        List<Integer> ids = List.of(1, 2, 3);
        List<McpServerDto> expected = List.of(new McpServerDto(), new McpServerDto());
        when(mcpServerService.getMcpServerByIds(ids)).thenReturn(expected);

        List<McpServerDto> result = mcpServerRest.getMcpServerByIds(ids);
        assertEquals(expected, result);
        verify(mcpServerService).getMcpServerByIds(ids);
    }
}
