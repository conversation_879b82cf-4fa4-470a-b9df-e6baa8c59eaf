package com.enttribe.promptanalyzer.rest.impl;

import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.service.TimeParserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class TimeParserRestImplTest {

    @Mock
    private TimeParserService timeParserService;

    @InjectMocks
    private TimeParserRestImpl timeParserRest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testParseTime_validInput() {
        Map<String, String> request = Map.of("userQuery", "last quarter");
        String expectedResult = "Q1 2025";

        when(timeParserService.parseTimeFromQuery(request)).thenReturn(expectedResult);

        Map<String, String> result = timeParserRest.parseTime(request);

        assertEquals(Map.of(PromptConstants.RESULT, expectedResult), result);
        verify(timeParserService, times(1)).parseTimeFromQuery(request);
    }

    @Test
    void testParseTime_emptyQuery() {
        Map<String, String> request = Map.of("userQuery", "");

        Map<String, String> result = timeParserRest.parseTime(request);

        assertEquals(Map.of(PromptConstants.RESULT, "No query provided"), result);
        verifyNoInteractions(timeParserService);
    }

    @Test
    void testParseTime_nullQuery() {
        Map<String, String> request = Map.of(); // No userQuery key

        Map<String, String> result = timeParserRest.parseTime(request);

        assertEquals(Map.of(PromptConstants.RESULT, "No query provided"), result);
        verifyNoInteractions(timeParserService);
    }

    @Test
    void testAddYearIfMissing() {
        Map<String, String> request = Map.of("userQuery", "March");
        String expected = "March 2025";

        when(timeParserService.addYearIfMissing(request)).thenReturn(expected);

        Map<String, String> result = timeParserRest.addYearIfMissing(request);

        assertEquals(Map.of(PromptConstants.RESULT, expected), result);
        verify(timeParserService, times(1)).addYearIfMissing(request);
    }
}
